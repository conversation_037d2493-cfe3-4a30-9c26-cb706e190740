.PHONY: build run


# 手动指定版本号构建
# 使用方式: make build VERSION=1.2.3
build:
	@if [ -z "$(VERSION)" ]; then \
		echo "Error: VERSION is required. Usage: make build VERSION=1.2.3"; \
		exit 1; \
	fi
	@if ! echo "$(VERSION)" | grep -qE '^[0-9]+\.[0-9]+\.[0-9]+$$'; then \
		echo "Error: VERSION must follow semantic versioning format (e.g., 1.0.0, 2.1.3)"; \
		echo "Current VERSION: $(VERSION)"; \
		exit 1; \
	fi
	@echo "Building version: $(VERSION)"
	docker build -f deploy/Dockerfile -t backend:$(VERSION) . && \
	docker tag backend:$(VERSION) registry.rcztcs.com/zjl/backend:$(VERSION) && \
	echo Docker image build success: backend:$(VERSION) && \
	docker push registry.rcztcs.com/zjl/backend:$(VERSION) && \
	echo Docker image pushed: registry.rcztcs.com/zjl/backend:$(VERSION)

# 运行容器 - 使用环境变量 VERSION 指定版本，默认为1
# 示例: make run 或  VERSION=2 make run
run:
	@echo "Checking for existing zjl-backend container..."
	@if docker ps -a --format "table {{.Names}}" | grep -q "^zjl-backend$$"; then \
		echo "Found existing zjl-backend container, stopping and removing..."; \
		docker stop zjl-backend 2>/dev/null || true; \
		docker rm zjl-backend 2>/dev/null || true; \
		echo "Existing container removed"; \
	else \
		echo "No existing zjl-backend container found"; \
	fi
	@echo "Creating network if not exists..."
	@docker network create zjlnetwork 2>/dev/null || true
	@echo "Starting zjl-backend container..."
	docker run -d \
		--name zjl-backend \
		--network zjlnetwork \
		-p 8080:8080 \
		-v ./uploads:/app/uploads \
		-e TZ=Asia/Shanghai \
		--restart unless-stopped \
		registry.rcztcs.com/zjl/backend:$(if $(VERSION),$(VERSION),1)
	@echo "Container backend started successfully"
