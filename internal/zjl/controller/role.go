package controller

import (
	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/svc"
	"gitee.com/rcztcs/zjl/internal/zjl/types"
	"github.com/gin-gonic/gin"
)

// registerRoleRoutes 注册角色相关的路由
func registerRoleRoutes(r *gin.RouterGroup, svc *svc.ServiceContext) {
	roles := r.Group("/roles")
	{
		// 角色资源管理
		roles.POST("", CreateRole(svc))    // POST /roles - 创建角色
		roles.GET("", ListRoles(svc))      // GET /roles - 获取角色列表
		roles.PUT("/:id", UpdateRole(svc)) // PUT /roles/:id - 更新角色
	}

	// 角色用户查询
	//roles := r.Group("/roles")
	//{
	//	roles.GET("/:role_id/users", GetRoleUsers(svc)) // GET /roles/:role_id/users - 获取角色用户
	//}
}

// CreateRole 创建角色
func CreateRole(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req types.CreateRoleRequest

		// 统一参数解析
		err := utils.ParseRequest(ctx, &req)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		err = svc.RoleService.CreateRole(req.Name, req.Description)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 统一成功响应
		utils.ResponseSuccess(ctx, nil)
	}
}

// ListRoles 获取角色列表
func ListRoles(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		roles, total, err := svc.RoleService.ListRoles()
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 统一成功响应
		utils.ResponseList(ctx, roles, total)
	}
}

// UpdateRole 更新角色
func UpdateRole(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req types.UpdateRoleRequest

		// 获取路径参数
		err := ctx.ShouldBindUri(&req)
		if err != nil {
			utils.ResponseError(ctx, utils.ErrInvalidParams)
			return
		}

		// 解析请求体
		err = utils.ParseRequest(ctx, &req)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		err = svc.RoleService.UpdateRole(req.ID, req.Name, req.Description)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 统一成功响应
		utils.ResponseSuccess(ctx, nil)
	}
}

// GetRoleUsers 获取角色用户
func GetRoleUsers(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req types.GetRoleUsersRequest

		// 获取路径参数
		err := ctx.ShouldBindUri(&req)
		if err != nil {
			utils.ResponseError(ctx, utils.ErrInvalidParams)
			return
		}

		users, err := svc.UserRoleService.GetRoleUsers(req.RoleID)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		response := types.RoleUsersResponse{
			RoleID: req.RoleID,
			Users:  users,
		}

		// 统一成功响应
		utils.ResponseSuccess(ctx, response)
	}
}
