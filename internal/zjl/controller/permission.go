package controller

import (
	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/svc"
	"gitee.com/rcztcs/zjl/internal/zjl/types"
	"github.com/gin-gonic/gin"
)

// registerPermissionRoutes 注册权限相关的路由
func registerPermissionRoutes(r *gin.RouterGroup, svc *svc.ServiceContext) {
	permissions := r.Group("/permissions")
	{
		// 权限资源管理
		permissions.POST("", CreatePermission(svc))    // POST /permissions - 创建权限
		permissions.GET("", ListPermissions(svc))      // GET /permissions - 获取权限列表
		permissions.PUT("/:id", UpdatePermission(svc)) // PUT /permissions/:id - 更新权限
	}
}

// CreatePermission 创建权限
func CreatePermission(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req types.CreatePermissionRequest

		// 统一参数解析
		err := utils.ParseRequest(ctx, &req)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		permission, err := svc.PermissionService.CreatePermission(req.Name, req.Module, req.Resource, req.Action, req.Description)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 统一成功响应
		utils.ResponseSuccess(ctx, types.CreatePermissionResponse{Permission: permission})
	}
}

// ListPermissions 获取权限列表
func ListPermissions(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req types.ListPermissionsRequest

		// 解析查询参数
		err := ctx.ShouldBindQuery(&req)
		if err != nil {
			utils.ResponseError(ctx, utils.ErrInvalidParams)
			return
		}

		// 设置默认值
		if req.Page <= 0 {
			req.Page = 1
		}
		if req.PageSize <= 0 {
			req.PageSize = 10
		}

		permissions, total, err := svc.PermissionService.ListPermissions(req.Page, req.PageSize, req.Module, req.Resource, req.Action)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 统一成功响应
		utils.ResponseList(ctx, permissions, total)
	}
}

// UpdatePermission 更新权限
func UpdatePermission(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req types.UpdatePermissionRequest

		// 获取路径参数
		err := ctx.ShouldBindUri(&req)
		if err != nil {
			utils.ResponseError(ctx, utils.ErrInvalidParams)
			return
		}

		// 解析请求体
		err = utils.ParseRequest(ctx, &req)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		err = svc.PermissionService.UpdatePermission(req.ID, req.Name, req.Module, req.Resource, req.Action, req.Description)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 统一成功响应
		utils.ResponseSuccess(ctx, nil)
	}
}
