package controller

import (
	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/middleware"
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gitee.com/rcztcs/zjl/internal/zjl/svc"
	"gitee.com/rcztcs/zjl/internal/zjl/types"
	"github.com/gin-gonic/gin"
)

// registerUserRoutes 注册用户相关的路由
func registerUserRoutes(r *gin.RouterGroup, svc *svc.ServiceContext) {
	// 统一使用复数形式
	users := r.Group("/users")
	{
		// 认证相关接口
		users.POST("/login", Login(svc)) // POST /users/login - 用户登录
	}

	usersToken := r.Group("/users").Use(middleware.CheckToken(svc.JWTService))
	{
		usersToken.POST("/refresh", RefreshToken(svc)) // POST /users/refresh - 刷新token

		// 用户资源管理
		usersToken.GET("", ListUsers(svc))                   // GET /users - 获取用户列表
		usersToken.POST("", CreateUser(svc))                 // POST /users - 创建用户
		usersToken.GET("/:id", GetUserByID(svc))             // GET /users/:id - 获取指定用户
		usersToken.PUT("/:id/password", ResetPassword(svc))  // PUT /users/:id/password - 重置用户密码
		usersToken.PUT("/:id/status", UpdateUserStatus(svc)) // PUT /users/:id/status - 更新用户状态
	}
}

// Login 用户登录
func Login(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req types.LoginRequest

		// 统一参数解析
		err := utils.ParseRequest(ctx, &req)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		user, err := svc.UserService.Login(req.Username, req.Password)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 生成token对
		accessToken, accessExpire, refreshToken, _, err := svc.JWTService.GenerateTokenPair(user.ID)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		roleList, err := svc.UserRoleService.GetUserRoles(user.ID)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		roles := make([]string, 0, len(roleList))
		for _, role := range roleList {
			roles = append(roles, role.Name)
		}

		// 记录登录操作日志
		clientIP := utils.GetClientIP(ctx)
		userAgent := ctx.GetHeader("User-Agent")
		err = svc.OperationLogService.RecordOperation(
			user.ID,
			user.Username,
			model.OperationTypeLogin,
			clientIP,
			userAgent,
		)
		if err != nil {
			// 记录日志失败不影响登录流程，只记录错误
			// 可以考虑使用日志系统记录这个错误
		}

		res := types.LoginResponse{
			User:         user,
			AccessToken:  accessToken,
			Expires:      accessExpire,
			RefreshToken: refreshToken,
			Roles:        roles,
		}

		// 统一成功响应
		utils.ResponseSuccess(ctx, res)
	}
}

// RefreshToken 刷新访问令牌
func RefreshToken(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req types.RefreshTokenRequest

		// 统一参数解析
		err := utils.ParseRequest(ctx, &req)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 使用刷新令牌生成新的访问令牌
		accessToken, accessExpire, err := svc.JWTService.RefreshAccessToken(req.RefreshToken)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		res := types.RefreshTokenResponse{
			AccessToken: accessToken,
			Expires:     accessExpire,
		}

		// 统一成功响应
		utils.ResponseSuccess(ctx, res)
	}
}

// CreateUser 创建用户
func CreateUser(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req types.CreateUserRequest

		// 统一参数解析
		err := utils.ParseRequest(ctx, &req)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		err = svc.UserService.CreateUser(req.Nickname, req.Username, req.Password, req.Mobile)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 统一成功响应
		utils.ResponseSuccess(ctx, nil)
	}
}

// GetUserByID 根据ID获取用户信息
func GetUserByID(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 获取路径参数
		var req = struct {
			ID uint `uri:"id"`
		}{}
		err := utils.ParseRequest(ctx, &req)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		user, err := svc.UserService.GetUserInfo(req.ID)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		roles, err := svc.UserRoleService.GetUserRoles(req.ID)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		res := types.UserWithRoleResponse{
			User:     user,
			RoleList: roles,
		}

		// 统一成功响应
		utils.ResponseSuccess(ctx, res)
	}
}

// ResetPassword 重置用户密码
func ResetPassword(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 获取路径参数中的用户ID
		var uriReq = struct {
			ID uint `uri:"id" binding:"required"`
		}{}
		err := ctx.ShouldBindUri(&uriReq)
		if err != nil {
			utils.ResponseError(ctx, utils.ErrInvalidParams)
			return
		}

		// 获取请求体中的密码
		var bodyReq = struct {
			Password string `json:"password" binding:"required"`
		}{}
		err = utils.ParseRequest(ctx, &bodyReq)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		err = svc.UserService.ResetPassword(uriReq.ID, bodyReq.Password)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 统一成功响应
		utils.ResponseSuccess(ctx, nil)
	}
}

// ListUsers 获取用户列表
func ListUsers(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req types.ListUsersRequest

		// 解析查询参数
		err := ctx.ShouldBindQuery(&req)
		if err != nil {
			utils.ResponseError(ctx, utils.ErrInvalidParams)
			return
		}

		// 设置默认值
		if req.Page <= 0 {
			req.Page = 1
		}
		if req.PageSize <= 0 {
			req.PageSize = 10
		}

		users, total, err := svc.UserService.ListUsers(req.Page, req.PageSize, req.Nickname, req.Status)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 批量获取用户角色信息
		userIDs := make([]uint, 0, len(users))
		for _, user := range users {
			userIDs = append(userIDs, user.ID)
		}

		// 查询user-roles
		userRoles, err := svc.UserRoleService.GetUserRolesByUserIDs(userIDs)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		userRoleMap := make(map[uint][]uint)
		for _, role := range userRoles {
			userRoleMap[role.UserID] = append(userRoleMap[role.UserID], role.RoleID)
		}

		// 查询roles
		roles, _, err := svc.RoleService.ListRoles()
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 构建响应数据
		resList := make([]types.UserWithRoleResponse, 0, len(users))

		for _, user := range users {
			roleIDs := userRoleMap[user.ID]

			roleList := make([]*model.Role, 0, len(roleIDs))
			for _, roleID := range roleIDs {
				for _, role := range roles {
					if roleID == role.ID {
						roleList = append(roleList, role)
					}
				}
			}

			item := types.UserWithRoleResponse{
				User:     user,
				RoleList: roleList,
			}

			resList = append(resList, item)
		}

		// 统一成功响应
		utils.ResponseList(ctx, resList, total)
	}
}

// UpdateUserStatus 更新用户状态
func UpdateUserStatus(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 获取路径参数中的用户ID
		var uriReq = struct {
			ID uint `uri:"id" binding:"required"`
		}{}
		err := ctx.ShouldBindUri(&uriReq)
		if err != nil {
			utils.ResponseError(ctx, utils.ErrInvalidParams)
			return
		}

		// 获取请求体中的状态
		var bodyReq = struct {
			Status model.UserStatus `json:"status" binding:"required"`
		}{}
		err = utils.ParseRequest(ctx, &bodyReq)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 调用统一的更新状态方法
		err = svc.UserService.UpdateUserStatus(uriReq.ID, bodyReq.Status)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 统一成功响应
		utils.ResponseSuccess(ctx, nil)
	}
}
