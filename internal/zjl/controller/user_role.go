package controller

import (
	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/svc"
	"gitee.com/rcztcs/zjl/internal/zjl/types"
	"github.com/gin-gonic/gin"
)

// registerUserRoleRoutes 注册用户角色关联相关的路由
func registerUserRoleRoutes(r *gin.RouterGroup, svc *svc.ServiceContext) {
	// 用户角色关联管理
	users := r.Group("/user-roles")
	{
		users.PUT("/:user_id/roles", UpdateUserRoles(svc)) // PUT /user-roles/:user_id/roles - 更新用户角色关联
		users.GET("/:user_id/roles", GetUserRoles(svc))    // GET /user-roles/:user_id/roles - 获取用户角色
	}
}

// UpdateUserRoles 更新用户角色关联
func UpdateUserRoles(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req types.UpdateUserRolesRequest
		// 解析请求体
		err := utils.ParseRequest(ctx, &req)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		err = svc.UserRoleService.UpdateUserRoles(req.UserID, req.RoleIDs)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 统一成功响应
		utils.ResponseSuccess(ctx, nil)
	}
}

// GetUserRoles 获取用户角色
func GetUserRoles(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req types.GetUserRolesRequest

		// 获取路径参数
		err := ctx.ShouldBindUri(&req)
		if err != nil {
			utils.ResponseError(ctx, utils.ErrInvalidParams)
			return
		}

		roles, err := svc.UserRoleService.GetUserRoles(req.UserID)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		response := types.UserRolesResponse{
			UserID: req.UserID,
			Roles:  roles,
		}

		// 统一成功响应
		utils.ResponseSuccess(ctx, response)
	}
}
