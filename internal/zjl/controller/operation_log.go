package controller

import (
	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/svc"
	"gitee.com/rcztcs/zjl/internal/zjl/types"
	"github.com/gin-gonic/gin"
)

// registerOperationLogRoutes 注册操作日志相关的路由
func registerOperationLogRoutes(r *gin.RouterGroup, svc *svc.ServiceContext) {
	operationLogs := r.Group("/operation-logs")
	{
		// 操作日志查询
		operationLogs.GET("", ListOperationLogs(svc)) // GET /operation-logs - 获取操作日志列表
	}
}

// ListOperationLogs 获取操作日志列表
func ListOperationLogs(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req types.ListOperationLogsRequest

		err := utils.ParseRequest(ctx, &req)
		if err != nil {
			utils.ResponseError(ctx, utils.ErrInvalidParams)
			return
		}

		logs, total, err := svc.OperationLogService.ListOperationLogs(req.Page, req.PageSize, req.UserID, req.Operation)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 统一成功响应
		utils.ResponseList(ctx, logs, total)
	}
}
