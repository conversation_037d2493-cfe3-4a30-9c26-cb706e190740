package controller

import (
	"strconv"

	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/svc"
	"gitee.com/rcztcs/zjl/internal/zjl/types"
	"github.com/gin-gonic/gin"
)

// registerFileRoutes 注册文件相关的路由
func registerFileRoutes(r *gin.RouterGroup, svc *svc.ServiceContext) {
	files := r.Group("/files")
	{
		// 文件管理接口
		files.POST("/upload", UploadFile(svc))                  // POST /files/upload - 文件上传
		files.GET("/download/:storage_name", DownloadFile(svc)) // GET /files/download/:storage_name - 文件下载
		files.GET("/:storage_name", GetFileInfo(svc))           // GET /files/:storage_name - 获取文件信息
		files.GET("", ListFiles(svc))                           // GET /files - 获取文件列表
	}
}

// UploadFile 文件上传
func UploadFile(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 获取上传的文件
		file, err := ctx.FormFile("file")
		if err != nil {
			utils.ResponseError(ctx, utils.NewAppError(utils.ErrInvalidParamsCode, "请选择要上传的文件"))
			return
		}

		// TODO: 从JWT中获取用户ID，这里暂时使用固定值
		uploadUserID := uint(1)

		// 调用服务层上传文件
		fileRecord, err := svc.FileService.UploadFile(file, uploadUserID, "/task1")
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 构建响应
		response := &types.UploadFileResponse{
			StorageName: fileRecord.StorageName,
		}

		utils.ResponseSuccess(ctx, response)
	}
}

// DownloadFile 文件下载
func DownloadFile(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req types.DownloadFileRequest

		// 解析路径参数
		err := ctx.ShouldBindUri(&req)
		if err != nil {
			utils.ResponseError(ctx, utils.ErrInvalidParams)
			return
		}

		// 调用服务层下载文件
		fileRecord, filePath, err := svc.FileService.DownloadFile(req.StorageName)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 设置响应头
		ctx.Header("Content-Description", "File Transfer")
		ctx.Header("Content-Transfer-Encoding", "binary")
		ctx.Header("Content-Disposition", "attachment; filename="+fileRecord.FileName)
		ctx.Header("Content-Type", "application/octet-stream")
		ctx.Header("Content-Length", strconv.FormatInt(fileRecord.FileSize, 10))

		// 发送文件
		ctx.File(filePath)
	}
}

// GetFileInfo 获取文件信息
func GetFileInfo(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req types.GetFileInfoRequest

		// 解析路径参数
		err := ctx.ShouldBindUri(&req)
		if err != nil {
			utils.ResponseError(ctx, utils.ErrInvalidParams)
			return
		}

		// 调用服务层获取文件信息
		file, err := svc.FileService.GetFileInfo(req.StorageName)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		response := &types.GetFileInfoResponse{
			File: file,
		}

		utils.ResponseSuccess(ctx, response)
	}
}

// ListFiles 获取文件列表
func ListFiles(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req types.ListFilesRequest

		// 统一参数解析
		err := utils.ParseRequest(ctx, &req)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 调用服务层获取文件列表
		files, total, err := svc.FileService.ListFiles(req.Page, req.PageSize, req.UploadUserID, req.ContentType)
		if err != nil {
			utils.ResponseError(ctx, err)
			return
		}

		// 统一成功响应
		utils.ResponseList(ctx, files, total)
	}
}
