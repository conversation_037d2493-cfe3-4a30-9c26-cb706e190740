package types

import (
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"time"
)

// CreateTaskRequest 创建任务请求
type CreateTaskRequest struct {
	ReportID string `form:"reportId" binding:"required"` // 报告ID
	Token    string `form:"token" binding:"required"`    // 令牌
	Host     string `form:"host" binding:"required"`     // 主机
}

// GetTaskRequest 获取任务详情请求
type GetTaskRequest struct {
	ID uint `uri:"id" binding:"required"`
}

// GetTaskResponse 获取任务详情响应
type GetTaskResponse struct {
	*model.Task
	Username        string `json:"username"`
	Nickname        string `json:"nickname"`
	HighRiskCount   int    `json:"high_risk_count"`
	MediumRiskCount int    `json:"medium_risk_count"`
	LowRiskCount    int    `json:"low_risk_count"`
}

// ListTasksRequest 任务列表查询请求
type ListTasksRequest struct {
	Page      int              `form:"page"`       // 页码，默认1
	PageSize  int              `form:"page_size"`  // 每页数量，默认10
	UserID    uint             `form:"user_id"`    // 用户ID筛选，可选
	Status    model.TaskStatus `form:"status"`     // 状态筛选，可选
	StartTime *time.Time       `form:"start_time"` // 开始时间筛选，可选
	EndTime   *time.Time       `form:"end_time"`   // 结束时间筛选，可选
}

// SetDefaultPagination 设置默认分页参数
func (r *ListTasksRequest) SetDefaultPagination() {
	if r.Page <= 0 {
		r.Page = 1
	}
	if r.PageSize <= 0 {
		r.PageSize = 10
	}
}

// UpdateTaskStatusRequest 更新任务状态请求
type UpdateTaskStatusRequest struct {
	ID     uint             `uri:"id" binding:"required"`
	Status model.TaskStatus `json:"status" binding:"required"`
}

// UpdateTaskStatusResponse 更新任务状态响应
type UpdateTaskStatusResponse struct {
	Task *model.Task `json:"task"`
}

type ReportInfo struct {
	ReportID       string     `json:"report_id"`
	CreditCode     string     `json:"credit_code"`     // 统一社会信用代码
	TestPeriod     string     `json:"test_period"`     // 报告检测期间，例如：2023年10月-2025年07月
	CompanyName    string     `json:"company_name"`    //	 企业名称
	AccountingType string     `json:"accounting_type"` // 核算方式
	RiskList       []RiskInfo `json:"risk_list"`
}

type RiskInfo struct {
	RiskLevel          model.RiskLevel `json:"risk_level"`     // 风险类别（高、中、低风险）
	IndicatorType      string          `json:"indicator_type"` // 指标大类（如增值税、提示提醒等）
	IndicatorName      string          `json:"indicator_name"` // 指标名称
	RiskDetail         string          `json:"risk_detail"`
	RiskType           model.RiskType  `json:"risk_type"`
	ResponseSuggestion string          `json:"response_suggestion"` // 应对建议
	LegalBasis         string          `json:"legal_basis"`         // 法律依据
}
