package types

import "gitee.com/rcztcs/zjl/internal/zjl/model"

// UploadFileResponse 文件上传响应
type UploadFileResponse struct {
	StorageName string `json:"storage_name"` // 存储文件名
}

// DownloadFileRequest 文件下载请求
type DownloadFileRequest struct {
	StorageName string `uri:"storage_name" binding:"required"` // 存储文件名
}

// ListFilesRequest 文件列表查询请求
type ListFilesRequest struct {
	Page         int    `json:"page" form:"page"`                     // 页码，默认1
	PageSize     int    `json:"page_size" form:"page_size"`           // 每页数量，默认10
	UploadUserID *uint  `json:"upload_user_id" form:"upload_user_id"` // 上传用户ID筛选，可选
	ContentType  string `json:"content_type" form:"content_type"`     // 文件类型筛选，可选
}

// SetDefaultPagination 设置默认分页参数
func (r *ListFilesRequest) SetDefaultPagination() {
	if r.Page <= 0 {
		r.Page = 1
	}
	if r.PageSize <= 0 {
		r.PageSize = 10
	}
}

// ListFilesResponse 文件列表查询响应
type ListFilesResponse struct {
	Files []*model.File `json:"files"`
	Total int64         `json:"total"`
}

// GetFileInfoRequest 获取文件信息请求
type GetFileInfoRequest struct {
	StorageName string `uri:"storage_name" binding:"required"` // 存储文件名
}

// GetFileInfoResponse 获取文件信息响应
type GetFileInfoResponse struct {
	*model.File
}

// DeleteFileRequest 删除文件请求
type DeleteFileRequest struct {
	FileID uint `uri:"file_id" binding:"required"` // 文件ID
}
