package types

import (
	"gitee.com/rcztcs/zjl/internal/zjl/model"
)

// CreateRiskRequest 创建风险请求
type CreateRiskRequest struct {
	TaskID        uint            `json:"task_id" binding:"required"`        // 任务ID
	RiskLevel     model.RiskLevel `json:"risk_level" binding:"required"`     // 风险类别
	IndicatorType string          `json:"indicator_type" binding:"required"` // 指标大类
	IndicatorName string          `json:"indicator_name" binding:"required"` // 指标名称
}

// CreateRiskResponse 创建风险响应
type CreateRiskResponse struct {
	Risk *model.Risk `json:"risk"`
}

// GetRiskRequest 获取风险详情请求
type GetRiskRequest struct {
	ID uint `uri:"id" binding:"required"`
}

// GetRiskResponse 获取风险详情响应
type GetRiskResponse struct {
	Risk *model.Risk `json:"risk"`
}

// GetRisksByTaskIDRequest 根据任务ID获取风险列表请求
type GetRisksByTaskIDRequest struct {
	TaskID    uint            `uri:"task_id" binding:"required"` // 任务ID
	RiskLevel model.RiskLevel `form:"risk_level"`                // 风险等级（可选）
}

// GetRisksByTaskIDResponse 根据任务ID获取风险列表响应
type GetRisksByTaskIDResponse struct {
	Risks []*model.Risk `json:"risks"`
}

// ListRisksRequest 获取风险列表请求
type ListRisksRequest struct {
	Page          int             `form:"page,default=1"`       // 页码
	PageSize      int             `form:"page_size,default=10"` // 每页数量
	TaskID        uint            `form:"task_id"`              // 任务ID（可选）
	RiskLevel     model.RiskLevel `form:"risk_level"`           // 风险类别（可选）
	IndicatorType string          `form:"indicator_type"`       // 指标大类（可选）
}

// ListRisksResponse 获取风险列表响应
type ListRisksResponse struct {
	Risks []*model.Risk `json:"risks"`
	Total int64         `json:"total"`
}

// UpdateRiskRequest 更新风险请求
type UpdateRiskRequest struct {
	ID            uint            `uri:"id" binding:"required"`              // 风险ID
	RiskLevel     model.RiskLevel `json:"risk_level" binding:"required"`     // 风险类别
	IndicatorType string          `json:"indicator_type" binding:"required"` // 指标大类
	IndicatorName string          `json:"indicator_name" binding:"required"` // 指标名称
}

// DeleteRiskRequest 删除风险请求
type DeleteRiskRequest struct {
	ID uint `uri:"id" binding:"required"`
}
