package types

// CreateRoleRequest 创建角色请求
type CreateRoleRequest struct {
	Name        string `json:"name" binding:"required"`        // 角色名称
	Description string `json:"description" binding:"required"` // 角色描述
}

// UpdateRoleRequest 更新角色请求
type UpdateRoleRequest struct {
	ID          uint   `uri:"id" binding:"required"`
	Name        string `json:"name" binding:"required"`
	Description string `json:"description" binding:"required"`
}

// DeleteRoleRequest 删除角色请求
type DeleteRoleRequest struct {
	ID uint `uri:"id" binding:"required"`
}
