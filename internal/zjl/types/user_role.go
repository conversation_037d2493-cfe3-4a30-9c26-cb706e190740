package types

import "gitee.com/rcztcs/zjl/internal/zjl/model"

// UpdateUserRolesRequest 更新用户角色关联请求
type UpdateUserRolesRequest struct {
	UserID  uint   `uri:"user_id" binding:"required"`   // 用户ID
	RoleIDs []uint `json:"role_ids" binding:"required"` // 角色ID列表
}

// GetUserRolesRequest 获取用户角色请求
type GetUserRolesRequest struct {
	UserID uint `uri:"user_id" binding:"required"` // 用户ID
}

// GetRoleUsersRequest 获取角色用户请求
type GetRoleUsersRequest struct {
	RoleID uint `uri:"role_id" binding:"required"` // 角色ID
}

// UserRolesResponse 用户角色响应
type UserRolesResponse struct {
	UserID uint          `json:"user_id"` // 用户ID
	Roles  []*model.Role `json:"roles"`   // 角色列表
}

// RoleUsersResponse 角色用户响应
type RoleUsersResponse struct {
	RoleID uint          `json:"role_id"` // 角色ID
	Users  []*model.User `json:"users"`   // 用户列表
}
