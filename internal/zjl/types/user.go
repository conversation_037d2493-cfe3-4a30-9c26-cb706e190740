package types

import "gitee.com/rcztcs/zjl/internal/zjl/model"

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	User         *model.User `json:"user"`
	Roles        []string    `json:"roles"`
	Permissions  []string    `json:"permissions"`
	AccessToken  string      `json:"access_token"`
	Expires      int64       `json:"expires"`
	RefreshToken string      `json:"refresh_token"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Nickname string `json:"nickname"`
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
	Mobile   string `json:"mobile"`
}

// UserWithRoleResponse 获取用户响应
type UserWithRoleResponse struct {
	*model.User
	RoleList []*model.Role `json:"role_list"`
}

// RefreshTokenRequest 刷新token请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// RefreshTokenResponse 刷新token响应
type RefreshTokenResponse struct {
	AccessToken string `json:"access_token"`
	Expires     int64  `json:"expires"`
}

// ResetPasswordRequest 重置密码请求
type ResetPasswordRequest struct {
	Password string `json:"password" binding:"required"`
}

// ListUsersRequest 用户列表查询请求
type ListUsersRequest struct {
	Page     int               `json:"page" form:"page"`           // 页码，默认1
	PageSize int               `json:"page_size" form:"page_size"` // 每页数量，默认10
	Status   *model.UserStatus `json:"status" form:"status"`       // 用户状态筛选，可选
	Nickname string            `json:"nickname"`
}

// UpdateUserStatusRequest 更新用户状态请求
type UpdateUserStatusRequest struct {
	Status model.UserStatus `json:"status" binding:"required"` // 用户状态：1-启用，2-禁用
}
