package types

import "gitee.com/rcztcs/zjl/internal/zjl/model"

// ListOperationLogsRequest 操作日志列表查询请求
type ListOperationLogsRequest struct {
	Page      int    `json:"page" form:"page"`           // 页码，默认1
	PageSize  int    `json:"page_size" form:"page_size"` // 每页数量，默认10
	UserID    *uint  `json:"user_id" form:"user_id"`     // 用户ID筛选，可选
	Operation string `json:"operation" form:"operation"` // 操作类型筛选，可选
}

func (r *ListOperationLogsRequest) SetDefaultPagination() {
	if r.Page <= 0 {
		r.Page = 1
	}
	if r.PageSize <= 0 {
		r.PageSize = 40
	}
}

// OperationLogResponse 操作日志响应
type OperationLogResponse struct {
	*model.OperationLog
}
