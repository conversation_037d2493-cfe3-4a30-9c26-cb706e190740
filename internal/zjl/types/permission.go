package types

import "gitee.com/rcztcs/zjl/internal/zjl/model"

// CreatePermissionRequest 创建权限请求
type CreatePermissionRequest struct {
	Name        string `json:"name" binding:"required"`        // 权限名称
	Module      string `json:"module" binding:"required"`      // 所属模块
	Resource    string `json:"resource" binding:"required"`    // 资源类型
	Action      string `json:"action" binding:"required"`      // 操作类型
	Description string `json:"description" binding:"required"` // 描述
}

// CreatePermissionResponse 创建权限响应
type CreatePermissionResponse struct {
	Permission *model.Permission `json:"permission"`
}

// ListPermissionsRequest 权限列表查询请求
type ListPermissionsRequest struct {
	Page     int    `json:"page" form:"page"`           // 页码，默认1
	PageSize int    `json:"page_size" form:"page_size"` // 每页数量，默认10
	Module   string `json:"module" form:"module"`       // 模块筛选，可选
	Resource string `json:"resource" form:"resource"`   // 资源筛选，可选
	Action   string `json:"action" form:"action"`       // 操作筛选，可选
}

// ListPermissionsResponse 权限列表查询响应
type ListPermissionsResponse struct {
	Permissions []*model.Permission `json:"permissions"`
	Total       int64               `json:"total"`
}

// GetPermissionRequest 获取权限请求
type GetPermissionRequest struct {
	ID uint `uri:"id" binding:"required"`
}

// GetPermissionResponse 获取权限响应
type GetPermissionResponse struct {
	Permission *model.Permission `json:"permission"`
}

// UpdatePermissionRequest 更新权限请求
type UpdatePermissionRequest struct {
	ID          uint   `uri:"id" binding:"required"`
	Name        string `json:"name" binding:"required"`
	Module      string `json:"module" binding:"required"`
	Resource    string `json:"resource" binding:"required"`
	Action      string `json:"action" binding:"required"`
	Description string `json:"description" binding:"required"`
}
