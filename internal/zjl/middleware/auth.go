package middleware

import (
	"strings"

	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/service"
	"github.com/gin-gonic/gin"
)

// CheckToken token
func CheckToken(jwtService service.JWTService) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 从请求头中获取Authorization
		authHeader := ctx.GetHeader("Authorization")
		if authHeader == "" {
			utils.ResponseError(ctx, utils.ErrUnauthorized)
			ctx.Abort()
			return
		}

		// 检查Bearer前缀
		const bearerPrefix = "Bearer "
		if !strings.HasPrefix(authHeader, bearerPrefix) {
			utils.ResponseError(ctx, utils.NewAppError(utils.ErrUnauthorizedCode, "无效的认证格式"))
			ctx.Abort()
			return
		}

		// 提取token
		token := strings.TrimPrefix(authHeader, bearerPrefix)
		if token == "" {
			utils.ResponseError(ctx, utils.NewAppError(utils.ErrUnauthorizedCode, "token不能为空"))
			ctx.Abort()
			return
		}

		// 验证token
		if !jwtService.ValidateToken(token) {
			utils.ResponseError(ctx, utils.NewAppError(utils.ErrUnauthorizedCode, "token无效或已过期"))
			ctx.Abort()
			return
		}

		// 从token中获取用户ID
		userID, err := jwtService.GetUserIDFromToken(token)
		if err != nil {
			utils.ResponseError(ctx, utils.NewAppError(utils.ErrUnauthorizedCode, "无法解析用户信息"))
			ctx.Abort()
			return
		}

		// 将用户ID存储到上下文中
		ctx.Set("userID", userID)
		ctx.Set("token", token)

		// 继续处理请求
		ctx.Next()
	}
}
