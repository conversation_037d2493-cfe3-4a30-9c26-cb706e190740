package model

import "gorm.io/gorm"

type OperationType string

const (
	// OperationTypeLogin 登录
	OperationTypeLogin OperationType = "login"
)

// OperationLog 操作日志模型
type OperationLog struct {
	gorm.Model
	UserID    uint          `gorm:"column:user_id;type:int unsigned;not null;index" json:"user_id"`   // 用户ID
	Username  string        `gorm:"column:username;type:varchar(100);not null;index" json:"username"` // 用户名
	Operation OperationType `gorm:"column:operation;type:varchar(100);not null" json:"operation"`     // 操作类型
	IP        string        `gorm:"column:ip;type:varchar(45);not null" json:"ip"`                    // IP地址
	UserAgent string        `gorm:"column:user_agent;type:varchar(500)" json:"user_agent"`            // 用户代理
}
