package model

import (
	"gitee.com/rcztcs/zjl/internal/pkg/logger"
	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"
)

var mySQLDb *gorm.DB

func Init(path string) error {
	var err error
	mySQLDb, err = utils.MySQLConnect(path, gormLogger.Info, logger.Logger())
	if err != nil {
		return err
	}

	// 自动迁移数据库表结构
	err = autoMigrate()
	if err != nil {
		return err
	}

	return nil
}

// autoMigrate 自动迁移数据库表结构
func autoMigrate() error {
	return mySQLDb.AutoMigrate(
		&User{},
		&Role{},
		&Permission{},
		&UserRole{},
		&OperationLog{},
		&Task{},
		&File{},
		&Risk{},
	)
}

func GetDB() *gorm.DB {
	return mySQLDb
}
