package model

import "gorm.io/gorm"

// Role 角色模型
type Role struct {
	gorm.Model
	Name        string `gorm:"column:name;type:varchar(100);not null;uniqueIndex" json:"name"`   // 角色名称
	Description string `gorm:"column:description;type:varchar(255);not null" json:"description"` // 描述
}

// UserRole 用户角色关联表
type UserRole struct {
	gorm.Model
	UserID uint `gorm:"column:user_id;type:int unsigned;not null;index" json:"user_id"` // 用户ID
	RoleID uint `gorm:"column:role_id;type:int unsigned;not null;index" json:"role_id"` // 角色ID
}

// Permission 权限模型 - 独立的权限定义
type Permission struct {
	gorm.Model
	Name        string `gorm:"column:name;type:varchar(100);not null;uniqueIndex" json:"name"`   // 权限名称
	Module      string `gorm:"column:module;type:varchar(50);not null;index" json:"module"`      // 所属模块
	Resource    string `gorm:"column:resource;type:varchar(50);not null;index" json:"resource"`  // 资源类型
	Action      string `gorm:"column:action;type:varchar(50);not null;index" json:"action"`      // 操作类型
	Description string `gorm:"column:description;type:varchar(255);not null" json:"description"` // 描述
}

// RolePermission 角色权限关联表 - 多对多关系
type RolePermission struct {
	gorm.Model
	RoleID       uint `gorm:"column:role_id;type:int unsigned;not null;index" json:"role_id"`             // 角色ID
	PermissionID uint `gorm:"column:permission_id;type:int unsigned;not null;index" json:"permission_id"` // 权限ID
}
