package dao

import (
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gorm.io/gorm"
)

// UserRoleDao 用户角色关联数据访问层接口
type UserRoleDao interface {
	// CreateUserRoles 创建用户角色关联（批量）
	CreateUserRoles(userID uint, roleIDs []uint) error

	// DeleteUserRoles 删除用户角色关联（批量）
	DeleteUserRoles(userID uint, roleIDs []uint) error

	// DeleteAllUserRoles 删除用户的所有角色关联
	DeleteAllUserRoles(userID uint) error

	// GetUserRoles 获取用户的所有角色
	GetUserRoles(userID uint) ([]*model.Role, error)

	// GetRoleUsers 获取角色下的所有用户
	GetRoleUsers(roleID uint) ([]*model.User, error)

	// IsUserRoleExist 检查用户角色关联是否存在
	IsUserRoleExist(userID, roleID uint) (bool, error)

	// GetUserRolesByUserIDs 批量获取多个用户的角色信息
	GetUserRolesByUserIDs(userIDs []uint) ([]*model.UserRole, error)
}

// userRoleDao 用户角色关联数据访问层实现
type userRoleDao struct {
	db *gorm.DB
}

// NewUserRoleDao 创建用户角色关联数据访问层实例
func NewUserRoleDao(db *gorm.DB) UserRoleDao {
	return &userRoleDao{
		db: db,
	}
}

// CreateUserRoles 创建用户角色关联（批量）
func (d *userRoleDao) CreateUserRoles(userID uint, roleIDs []uint) error {
	if len(roleIDs) == 0 {
		return nil
	}

	var userRoles []model.UserRole
	for _, roleID := range roleIDs {
		userRoles = append(userRoles, model.UserRole{
			UserID: userID,
			RoleID: roleID,
		})
	}

	return d.db.CreateInBatches(&userRoles, 100).Error
}

// DeleteUserRoles 删除用户角色关联（批量）
func (d *userRoleDao) DeleteUserRoles(userID uint, roleIDs []uint) error {
	if len(roleIDs) == 0 {
		return nil
	}

	return d.db.Where("user_id = ? AND role_id IN ?", userID, roleIDs).Unscoped().
		Delete(&model.UserRole{}).Error
}

// DeleteAllUserRoles 删除用户的所有角色关联
func (d *userRoleDao) DeleteAllUserRoles(userID uint) error {
	return d.db.Where("user_id = ?", userID).Delete(&model.UserRole{}).Error
}

// GetUserRoles 获取用户的所有角色
func (d *userRoleDao) GetUserRoles(userID uint) ([]*model.Role, error) {
	var roles []*model.Role
	err := d.db.Table("roles").
		Joins("INNER JOIN user_roles ON roles.id = user_roles.role_id").
		Where("user_roles.user_id = ? AND user_roles.deleted_at IS NULL", userID).
		Find(&roles).Error
	return roles, err
}

// GetRoleUsers 获取角色下的所有用户
func (d *userRoleDao) GetRoleUsers(roleID uint) ([]*model.User, error) {
	var users []*model.User
	err := d.db.Table("users").
		Joins("INNER JOIN user_roles ON users.id = user_roles.user_id").
		Where("user_roles.role_id = ? AND user_roles.deleted_at IS NULL", roleID).
		Find(&users).Error
	return users, err
}

// IsUserRoleExist 检查用户角色关联是否存在
func (d *userRoleDao) IsUserRoleExist(userID, roleID uint) (bool, error) {
	var count int64
	err := d.db.Model(&model.UserRole{}).
		Where("user_id = ? AND role_id = ?", userID, roleID).
		Count(&count).Error
	return count > 0, err
}

// GetUserRolesByUserIDs 批量获取多个用户的角色信息
func (d *userRoleDao) GetUserRolesByUserIDs(userIDs []uint) ([]*model.UserRole, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}
	var users []*model.UserRole

	// 构建查询条件
	query := d.db.Model(&model.UserRole{})
	err := query.Where("user_id IN ?", userIDs).Find(&users).Error
	if err != nil {
		return nil, err
	}
	return users, nil
}
