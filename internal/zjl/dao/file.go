package dao

import (
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gorm.io/gorm"
	"time"
)

// FileDao 文件数据访问层接口
type FileDao interface {
	// Create 创建文件记录
	Create(file *model.File) error

	// GetByID 根据ID获取文件
	GetByID(id uint) (*model.File, error)

	// GetByStorageName 根据存储文件名获取文件
	GetByStorageName(storageName string) (*model.File, error)

	// GetByHash 根据文件哈希获取文件
	GetByHash(hash string) (*model.File, error)

	// UpdateDownloadInfo 更新下载信息
	UpdateDownloadInfo(id uint) error

	// List 分页查询文件列表
	List(page, pageSize int, uploadUserID *uint, contentType string) ([]*model.File, error)

	// Count 统计文件数量
	Count(uploadUserID *uint, contentType string) (int64, error)

	// UpdateStatus 更新文件状态
	UpdateStatus(id uint, status model.FileStatus) error

	// Delete 软删除文件
	Delete(id uint) error
}

// fileDao 文件数据访问层实现
type fileDao struct {
	db *gorm.DB
}

// NewFileDao 创建文件数据访问层实例
func NewFileDao(db *gorm.DB) FileDao {
	return &fileDao{
		db: db,
	}
}

// Create 创建文件记录
func (d *fileDao) Create(file *model.File) error {
	return d.db.Create(file).Error
}

// GetByID 根据ID获取文件
func (d *fileDao) GetByID(id uint) (*model.File, error) {
	var file model.File
	err := d.db.Where("id = ? AND status = ?", id, model.FileStatusActive).First(&file).Error
	if err != nil {
		return nil, err
	}
	return &file, nil
}

// GetByStorageName 根据存储文件名获取文件
func (d *fileDao) GetByStorageName(storageName string) (*model.File, error) {
	var file model.File
	err := d.db.Where("storage_name = ? AND status = ?", storageName, model.FileStatusActive).First(&file).Error
	if err != nil {
		return nil, err
	}
	return &file, nil
}

// GetByHash 根据文件哈希获取文件
func (d *fileDao) GetByHash(hash string) (*model.File, error) {
	var file model.File
	err := d.db.Where("file_hash = ? AND status = ?", hash, model.FileStatusActive).First(&file).Error
	if err != nil {
		return nil, err
	}
	return &file, nil
}

// UpdateDownloadInfo 更新下载信息
func (d *fileDao) UpdateDownloadInfo(id uint) error {
	now := time.Now()
	return d.db.Model(&model.File{}).Where("id = ?", id).Updates(map[string]interface{}{
		"download_count":   gorm.Expr("download_count + 1"),
		"last_download_at": &now,
	}).Error
}

// List 分页查询文件列表
func (d *fileDao) List(page, pageSize int, uploadUserID *uint, contentType string) ([]*model.File, error) {
	var files []*model.File
	query := d.db.Where("status = ?", model.FileStatusActive)

	if uploadUserID != nil {
		query = query.Where("upload_user_id = ?", *uploadUserID)
	}

	if contentType != "" {
		query = query.Where("content_type LIKE ?", "%"+contentType+"%")
	}

	offset := (page - 1) * pageSize
	err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&files).Error
	return files, err
}

// Count 统计文件数量
func (d *fileDao) Count(uploadUserID *uint, contentType string) (int64, error) {
	var count int64
	query := d.db.Model(&model.File{}).Where("status = ?", model.FileStatusActive)

	if uploadUserID != nil {
		query = query.Where("upload_user_id = ?", *uploadUserID)
	}

	if contentType != "" {
		query = query.Where("content_type LIKE ?", "%"+contentType+"%")
	}

	err := query.Count(&count).Error
	return count, err
}

// UpdateStatus 更新文件状态
func (d *fileDao) UpdateStatus(id uint, status model.FileStatus) error {
	return d.db.Model(&model.File{}).Where("id = ?", id).Update("status", status).Error
}

// Delete 软删除文件
func (d *fileDao) Delete(id uint) error {
	return d.UpdateStatus(id, model.FileStatusDeleted)
}
