package dao

import (
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gorm.io/gorm"
)

// PermissionDao 权限数据访问层接口
type PermissionDao interface {
	// Create 创建权限
	Create(permission *model.Permission) error

	// GetByID 根据ID获取权限
	GetByID(id uint) (*model.Permission, error)

	// List 分页查询权限列表
	List(page, pageSize int, module, resource, action string) ([]*model.Permission, error)

	// Count 统计权限数量
	Count(module, resource, action string) (int64, error)

	// Update 更新权限信息
	Update(permission *model.Permission) error
}

// permissionDao 权限数据访问层实现
type permissionDao struct {
	db *gorm.DB
}

// NewPermissionDao 创建权限数据访问层实例
func NewPermissionDao(db *gorm.DB) PermissionDao {
	return &permissionDao{
		db: db,
	}
}

// Create 创建权限
func (d *permissionDao) Create(permission *model.Permission) error {
	return d.db.Create(permission).Error
}

// GetByID 根据ID获取权限
func (d *permissionDao) GetByID(id uint) (*model.Permission, error) {
	var permission model.Permission
	err := d.db.Where("id = ?", id).First(&permission).Error
	if err != nil {
		return nil, err
	}
	return &permission, nil
}

// List 分页查询权限列表
func (d *permissionDao) List(page, pageSize int, module, resource, action string) ([]*model.Permission, error) {
	var permissions []*model.Permission
	query := d.db.Model(&model.Permission{})

	// 根据模块筛选
	if module != "" {
		query = query.Where("module LIKE ?", "%"+module+"%")
	}

	// 根据资源筛选
	if resource != "" {
		query = query.Where("resource LIKE ?", "%"+resource+"%")
	}

	// 根据操作筛选
	if action != "" {
		query = query.Where("action LIKE ?", "%"+action+"%")
	}

	offset := (page - 1) * pageSize
	err := query.Offset(offset).Limit(pageSize).Find(&permissions).Error
	return permissions, err
}

// Count 统计权限数量
func (d *permissionDao) Count(module, resource, action string) (int64, error) {
	var count int64
	query := d.db.Model(&model.Permission{})

	// 根据模块筛选
	if module != "" {
		query = query.Where("module LIKE ?", "%"+module+"%")
	}

	// 根据资源筛选
	if resource != "" {
		query = query.Where("resource LIKE ?", "%"+resource+"%")
	}

	// 根据操作筛选
	if action != "" {
		query = query.Where("action LIKE ?", "%"+action+"%")
	}

	err := query.Count(&count).Error
	return count, err
}

// Update 更新权限信息
func (d *permissionDao) Update(permission *model.Permission) error {
	return d.db.Save(permission).Error
}
