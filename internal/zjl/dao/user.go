package dao

import (
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gorm.io/gorm"
	"time"
)

// UserDao 用户数据访问层接口
type UserDao interface {
	// Create 创建用户
	Create(user *model.User) error

	// GetByID 根据ID获取用户
	GetByID(id uint) (*model.User, error)

	// GetByUsername 根据用户名获取用户
	GetByUsername(username string) (*model.User, error)

	// GetByMobile 根据手机号获取用户
	GetByMobile(mobile string) (*model.User, error)

	// Update 更新用户信息
	Update(user *model.User) error

	// UpdatePassword 更新用户密码
	UpdatePassword(id uint, password string) error

	// UpdateStatus 更新用户状态
	UpdateStatus(id uint, status model.UserStatus) error

	// List 分页查询用户列表
	List(page, pageSize int, name string) ([]*model.User, error)

	// Count 统计用户数量
	Count(name string) (int64, error)

	// ListWithStatus 分页查询用户列表（支持状态筛选）
	ListWithStatus(page, pageSize int, name string, status *model.UserStatus) ([]*model.User, error)

	// ListByUsers 根据用户ID列表获取用户列表
	ListByUsers(userIDs []uint) ([]*model.User, error)

	// CountWithStatus 统计用户数量（支持状态筛选）
	CountWithStatus(name string, status *model.UserStatus) (int64, error)

	// IsUsernameExist 检查用户名是否存在
	IsUsernameExist(username string) (bool, error)

	// IsMobileExist 检查手机号是否存在
	IsMobileExist(mobile string) (bool, error)
}

// userDao 用户数据访问层实现
type userDao struct {
	db *gorm.DB
}

// NewUserDao 创建用户数据访问层实例
func NewUserDao(db *gorm.DB) UserDao {
	return &userDao{
		db: db,
	}
}

// Create 创建用户
func (d *userDao) Create(user *model.User) error {
	return d.db.Create(user).Error
}

// GetByID 根据ID获取用户
func (d *userDao) GetByID(id uint) (*model.User, error) {
	var user model.User
	err := d.db.Where("id = ?", id).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetByUsername 根据用户名获取用户
func (d *userDao) GetByUsername(username string) (*model.User, error) {
	var user model.User
	err := d.db.Where("username = ?", username).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetByMobile 根据手机号获取用户
func (d *userDao) GetByMobile(mobile string) (*model.User, error) {
	var user model.User
	err := d.db.Where("mobile = ?", mobile).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// Update 更新用户信息
func (d *userDao) Update(user *model.User) error {
	return d.db.Save(user).Error
}

// UpdatePassword 更新用户密码
func (d *userDao) UpdatePassword(id uint, password string) error {
	return d.db.Model(&model.User{}).Where("id = ?", id).Updates(map[string]interface{}{
		"password":   password,
		"updated_at": time.Now(),
	}).Error
}

// UpdateStatus 更新用户状态
func (d *userDao) UpdateStatus(id uint, status model.UserStatus) error {
	return d.db.Model(&model.User{}).Where("id = ?", id).Updates(map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}).Error
}

// List 分页查询用户列表
func (d *userDao) List(page, pageSize int, name string) ([]*model.User, error) {
	var users []*model.User

	// 构建查询条件
	query := d.db.Model(&model.User{})
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&users).Error
	return users, err
}

// Count 统计用户数量
func (d *userDao) Count(nickname string) (int64, error) {
	var count int64

	// 构建查询条件
	query := d.db.Model(&model.User{})
	if nickname != "" {
		query = query.Where("nickname LIKE ?", "%"+nickname+"%")
	}

	err := query.Count(&count).Error
	return count, err
}

// ListWithStatus 分页查询用户列表（支持状态筛选）
func (d *userDao) ListWithStatus(page, pageSize int, nickname string, status *model.UserStatus) ([]*model.User, error) {
	var users []*model.User

	// 构建查询条件
	query := d.db.Model(&model.User{})
	if nickname != "" {
		query = query.Where("nickname LIKE ?", "%"+nickname+"%")
	}
	if status != nil {
		query = query.Where("status = ?", *status)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&users).Error
	return users, err
}

// ListByUsers 根据用户ID列表获取用户列表
func (d *userDao) ListByUsers(userIDs []uint) ([]*model.User, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}
	var users []*model.User

	// 构建查询条件
	query := d.db.Model(&model.User{})
	err := query.Where("id IN ?", userIDs).Find(&users).Error
	if err != nil {
		return nil, err
	}
	return users, nil
}

// CountWithStatus 统计用户数量（支持状态筛选）
func (d *userDao) CountWithStatus(name string, status *model.UserStatus) (int64, error) {
	var count int64

	// 构建查询条件
	query := d.db.Model(&model.User{})
	if name != "" {
		query = query.Where("nickname LIKE ? OR username LIKE ?", "%"+name+"%", "%"+name+"%")
	}
	if status != nil {
		query = query.Where("status = ?", *status)
	}

	err := query.Count(&count).Error
	return count, err
}

// IsUsernameExist 检查用户名是否存在
func (d *userDao) IsUsernameExist(username string) (bool, error) {
	var count int64
	err := d.db.Model(&model.User{}).Where("username = ?", username).Count(&count).Error
	return count > 0, err
}

// IsMobileExist 检查手机号是否存在
func (d *userDao) IsMobileExist(mobile string) (bool, error) {
	var count int64
	err := d.db.Model(&model.User{}).Where("mobile = ?", mobile).Count(&count).Error
	return count > 0, err
}
