package dao

import (
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gorm.io/gorm"
)

// RoleDao 角色数据访问层接口
type RoleDao interface {
	// Create 创建角色
	Create(role *model.Role) error

	// GetByID 根据ID获取角色
	GetByID(id uint) (*model.Role, error)

	// GetByName 根据角色名获取角色
	GetByName(name string) (*model.Role, error)

	// List 分页查询角色列表
	List() ([]*model.Role, error)

	// Count 统计角色数量
	Count() (int64, error)

	// Update 更新角色信息
	Update(role *model.Role) error

	// Delete 删除角色
	Delete(id uint) error

	// IsNameExist 检查角色名是否存在
	IsNameExist(name string) (bool, error)
}

// roleDao 角色数据访问层实现
type roleDao struct {
	db *gorm.DB
}

// NewRoleDao 创建角色数据访问层实例
func NewRoleDao(db *gorm.DB) RoleDao {
	return &roleDao{
		db: db,
	}
}

// Create 创建角色
func (d *roleDao) Create(role *model.Role) error {
	return d.db.Create(role).Error
}

// GetByID 根据ID获取角色
func (d *roleDao) GetByID(id uint) (*model.Role, error) {
	var role model.Role
	err := d.db.Where("id = ?", id).First(&role).Error
	if err != nil {
		return nil, err
	}
	return &role, nil
}

// GetByName 根据角色名获取角色
func (d *roleDao) GetByName(name string) (*model.Role, error) {
	var role model.Role
	err := d.db.Where("name = ?", name).First(&role).Error
	if err != nil {
		return nil, err
	}
	return &role, nil
}

// List 分页查询角色列表
func (d *roleDao) List() ([]*model.Role, error) {
	var roles []*model.Role
	query := d.db.Model(&model.Role{})

	// 创建时间排序
	query = query.Order("created_at DESC")

	err := query.Find(&roles).Error
	return roles, err
}

// Count 统计角色数量
func (d *roleDao) Count() (int64, error) {
	var count int64
	query := d.db.Model(&model.Role{})

	err := query.Count(&count).Error
	return count, err
}

// Update 更新角色信息
func (d *roleDao) Update(role *model.Role) error {
	return d.db.Save(role).Error
}

// Delete 删除角色
func (d *roleDao) Delete(id uint) error {
	return d.db.Delete(&model.Role{}, id).Error
}

// IsNameExist 检查角色名是否存在
func (d *roleDao) IsNameExist(name string) (bool, error) {
	var count int64
	err := d.db.Model(&model.Role{}).Where("name = ?", name).Count(&count).Error
	return count > 0, err
}
