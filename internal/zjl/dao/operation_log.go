package dao

import (
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gorm.io/gorm"
)

// OperationLogDao 操作日志数据访问层接口
type OperationLogDao interface {
	// Create 创建操作日志
	Create(log *model.OperationLog) error

	// GetByID 根据ID获取操作日志
	GetByID(id uint) (*model.OperationLog, error)

	// List 分页查询操作日志列表
	List(page, pageSize int, userID *uint, operation string) ([]*model.OperationLog, error)

	// Count 统计操作日志数量
	Count(userID *uint, operation string) (int64, error)

	// ListByUserID 分页查询某个用户的操作日志
	ListByUserID(userID uint, page, pageSize int) ([]*model.OperationLog, error)

	// CountByUserID 统计某个用户的操作日志数量
	CountByUserID(userID uint) (int64, error)
}

// operationLogDao 操作日志数据访问层实现
type operationLogDao struct {
	db *gorm.DB
}

// NewOperationLogDao 创建操作日志数据访问层实例
func NewOperationLogDao(db *gorm.DB) OperationLogDao {
	return &operationLogDao{
		db: db,
	}
}

// Create 创建操作日志
func (d *operationLogDao) Create(log *model.OperationLog) error {
	return d.db.Create(log).Error
}

// GetByID 根据ID获取操作日志
func (d *operationLogDao) GetByID(id uint) (*model.OperationLog, error) {
	var log model.OperationLog
	err := d.db.Where("id = ?", id).First(&log).Error
	if err != nil {
		return nil, err
	}
	return &log, nil
}

// List 分页查询操作日志列表
func (d *operationLogDao) List(page, pageSize int, userID *uint, operation string) ([]*model.OperationLog, error) {
	var logs []*model.OperationLog
	query := d.db.Model(&model.OperationLog{})

	// 根据用户ID筛选
	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	// 根据操作类型筛选
	if operation != "" {
		query = query.Where("operation LIKE ?", "%"+operation+"%")
	}

	// 按创建时间倒序排列
	query = query.Order("created_at DESC")

	offset := (page - 1) * pageSize
	err := query.Offset(offset).Limit(pageSize).Find(&logs).Error
	return logs, err
}

// Count 统计操作日志数量
func (d *operationLogDao) Count(userID *uint, operation string) (int64, error) {
	var count int64
	query := d.db.Model(&model.OperationLog{})

	// 根据用户ID筛选
	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	// 根据操作类型筛选
	if operation != "" {
		query = query.Where("operation LIKE ?", "%"+operation+"%")
	}

	err := query.Count(&count).Error
	return count, err
}

// ListByUserID 分页查询某个用户的操作日志
func (d *operationLogDao) ListByUserID(userID uint, page, pageSize int) ([]*model.OperationLog, error) {
	var logs []*model.OperationLog
	query := d.db.Model(&model.OperationLog{}).Where("user_id = ?", userID)

	// 按创建时间倒序排列
	query = query.Order("created_at DESC")

	offset := (page - 1) * pageSize
	err := query.Offset(offset).Limit(pageSize).Find(&logs).Error
	return logs, err
}

// CountByUserID 统计某个用户的操作日志数量
func (d *operationLogDao) CountByUserID(userID uint) (int64, error) {
	var count int64
	err := d.db.Model(&model.OperationLog{}).Where("user_id = ?", userID).Count(&count).Error
	return count, err
}
