package dao

import (
	"gorm.io/gorm"
)

// DataDao 数据访问层接口
type DataDao interface {
	UserDao() UserDao
	RoleDao() RoleDao
	PermissionDao() PermissionDao
	UserRoleDao() UserRoleDao
	OperationLogDao() OperationLogDao
	TaskDao() TaskDao
	FileDao() FileDao
	RiskDao() RiskDao
}

type dataDao struct {
	db *gorm.DB
}

// InitDao 初始化数据访问层
func InitDao(d *gorm.DB) DataDao {
	return &dataDao{
		db: d,
	}
}

func (d *dataDao) UserDao() UserDao {
	return NewUserDao(d.db)
}

func (d *dataDao) RoleDao() RoleDao {
	return NewRoleDao(d.db)
}

func (d *dataDao) PermissionDao() PermissionDao {
	return NewPermissionDao(d.db)
}

func (d *dataDao) UserRoleDao() UserRoleDao {
	return NewUserRoleDao(d.db)
}

func (d *dataDao) OperationLogDao() OperationLogDao {
	return NewOperationLogDao(d.db)
}

func (d *dataDao) TaskDao() TaskDao {
	return NewTaskDao(d.db)
}

func (d *dataDao) FileDao() FileDao {
	return NewFileDao(d.db)
}

func (d *dataDao) RiskDao() RiskDao {
	return NewRiskDao(d.db)
}
