package service

import (
	"errors"
	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/dao"
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gorm.io/gorm"
)

// RoleService 角色服务层接口
type RoleService interface {
	// CreateRole 创建角色
	CreateRole(name, description string) error

	// GetRoleByID 根据ID获取角色
	GetRoleByID(id uint) (*model.Role, error)

	// ListRoles 分页查询角色列表
	ListRoles() ([]*model.Role, int64, error)

	// UpdateRole 更新角色
	UpdateRole(id uint, name, description string) error

	// DeleteRole 删除角色
	DeleteRole(id uint) error

	// GetRoleInfo 获取角色信息（保持向后兼容）
	GetRoleInfo(id uint) (*model.Role, error)
}

// roleService 角色服务层实现
type roleService struct {
	roleDao dao.RoleDao
}

// NewRoleService 创建角色服务层实例
func NewRoleService(roleDao dao.RoleDao) RoleService {
	return &roleService{
		roleDao: roleDao,
	}
}

// CreateRole 创建角色
func (s *roleService) CreateRole(name, description string) error {
	if name == "" {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "角色名称不能为空")
	}
	if description == "" {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "角色描述不能为空")
	}

	// 检查角色名是否已存在
	exists, err := s.roleDao.IsNameExist(name)
	if err != nil {
		return err
	}
	if exists {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "角色名称已存在")
	}

	// 创建角色
	role := &model.Role{
		Name:        name,
		Description: description,
	}

	err = s.roleDao.Create(role)
	if err != nil {
		return err
	}

	return nil
}

// GetRoleByID 根据ID获取角色
func (s *roleService) GetRoleByID(id uint) (*model.Role, error) {
	if id == 0 {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "角色ID不能为空")
	}

	role, err := s.roleDao.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, utils.NewAppError(utils.ErrNotFoundCode, "角色不存在")
		}
		return nil, err
	}

	return role, nil
}

// ListRoles 分页查询角色列表
func (s *roleService) ListRoles() ([]*model.Role, int64, error) {
	// 查询角色列表
	roles, err := s.roleDao.List()
	if err != nil {
		return nil, 0, err
	}

	// 查询总数
	total, err := s.roleDao.Count()
	if err != nil {
		return nil, 0, err
	}

	return roles, total, nil
}

// UpdateRole 更新角色
func (s *roleService) UpdateRole(id uint, name, description string) error {
	if id == 0 {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "角色ID不能为空")
	}
	if name == "" {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "角色名称不能为空")
	}
	if description == "" {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "角色描述不能为空")
	}

	// 检查角色是否存在
	role, err := s.roleDao.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return utils.NewAppError(utils.ErrNotFoundCode, "角色不存在")
		}
		return err
	}

	// 如果名称和描述都没有变化，直接返回
	if role.Name == name && role.Description == description {
		return nil
	}

	// 如果名称有变化，检查新名称是否已存在
	if role.Name != name {
		exists, err := s.roleDao.IsNameExist(name)
		if err != nil {
			return err
		}
		if exists {
			return utils.NewAppError(utils.ErrInvalidParamsCode, "角色名称已存在")
		}
	}

	// 更新角色
	role.Name = name
	role.Description = description
	return s.roleDao.Update(role)
}

// DeleteRole 删除角色
func (s *roleService) DeleteRole(id uint) error {
	if id == 0 {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "角色ID不能为空")
	}

	// 检查角色是否存在
	_, err := s.roleDao.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return utils.NewAppError(utils.ErrNotFoundCode, "角色不存在")
		}
		return err
	}

	// 删除角色
	return s.roleDao.Delete(id)
}

// GetRoleInfo 获取角色信息（保持向后兼容）
func (s *roleService) GetRoleInfo(id uint) (*model.Role, error) {
	return s.GetRoleByID(id)
}
