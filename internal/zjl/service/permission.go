package service

import (
	"errors"
	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/dao"
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gorm.io/gorm"
)

// PermissionService 权限服务层接口
type PermissionService interface {
	// CreatePermission 创建权限
	CreatePermission(name, module, resource, action, description string) (*model.Permission, error)

	// ListPermissions 分页查询权限列表
	ListPermissions(page, pageSize int, module, resource, action string) ([]*model.Permission, int64, error)

	// UpdatePermission 更新权限
	UpdatePermission(id uint, name, module, resource, action, description string) error
}

// permissionService 权限服务层实现
type permissionService struct {
	permissionDao dao.PermissionDao
}

// NewPermissionService 创建权限服务层实例
func NewPermissionService(permissionDao dao.PermissionDao) PermissionService {
	return &permissionService{
		permissionDao: permissionDao,
	}
}

// CreatePermission 创建权限
func (s *permissionService) CreatePermission(name, module, resource, action, description string) (*model.Permission, error) {
	if name == "" {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "权限名称不能为空")
	}
	if module == "" {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "模块不能为空")
	}
	if resource == "" {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "资源不能为空")
	}
	if action == "" {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "操作不能为空")
	}
	if description == "" {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "描述不能为空")
	}

	// 创建权限
	permission := &model.Permission{
		Name:        name,
		Module:      module,
		Resource:    resource,
		Action:      action,
		Description: description,
	}

	err := s.permissionDao.Create(permission)
	if err != nil {
		return nil, err
	}

	return permission, nil
}

// ListPermissions 分页查询权限列表
func (s *permissionService) ListPermissions(page, pageSize int, module, resource, action string) ([]*model.Permission, int64, error) {
	// 参数校验
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	// 查询权限列表
	permissions, err := s.permissionDao.List(page, pageSize, module, resource, action)
	if err != nil {
		return nil, 0, err
	}

	// 查询总数
	total, err := s.permissionDao.Count(module, resource, action)
	if err != nil {
		return nil, 0, err
	}

	return permissions, total, nil
}

// UpdatePermission 更新权限
func (s *permissionService) UpdatePermission(id uint, name, module, resource, action, description string) error {
	if id == 0 {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "权限ID不能为空")
	}
	if name == "" {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "权限名称不能为空")
	}
	if module == "" {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "模块不能为空")
	}
	if resource == "" {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "资源不能为空")
	}
	if action == "" {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "操作不能为空")
	}
	if description == "" {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "描述不能为空")
	}

	// 检查权限是否存在
	permission, err := s.permissionDao.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return utils.NewAppError(utils.ErrNotFoundCode, "权限不存在")
		}
		return err
	}

	// 更新权限
	permission.Name = name
	permission.Module = module
	permission.Resource = resource
	permission.Action = action
	permission.Description = description

	return s.permissionDao.Update(permission)
}
