package service

import (
	"errors"
	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/dao"
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gorm.io/gorm"
)

// UserRoleService 用户角色关联服务层接口
type UserRoleService interface {
	// UpdateUserRoles 更新用户角色关联（替换原有的创建和删除功能）
	UpdateUserRoles(userID uint, roleIDs []uint) error

	// GetUserRoles 获取用户的所有角色
	GetUserRoles(userID uint) ([]*model.Role, error)

	// GetRoleUsers 获取角色下的所有用户
	GetRoleUsers(roleID uint) ([]*model.User, error)

	// GetUserRolesByUserIDs 批量获取多个用户的角色信息
	GetUserRolesByUserIDs(userIDs []uint) ([]*model.UserRole, error)
}

// userRoleService 用户角色关联服务层实现
type userRoleService struct {
	userRoleDao dao.UserRoleDao
	userDao     dao.UserDao
	roleDao     dao.RoleDao
}

// NewUserRoleService 创建用户角色关联服务层实例
func NewUserRoleService(userRoleDao dao.UserRoleDao, userDao dao.UserDao, roleDao dao.RoleDao) UserRoleService {
	return &userRoleService{
		userRoleDao: userRoleDao,
		userDao:     userDao,
		roleDao:     roleDao,
	}
}

// UpdateUserRoles 更新用户角色关联（替换原有的创建和删除功能）
func (s *userRoleService) UpdateUserRoles(userID uint, roleIDs []uint) error {
	if userID == 0 {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "用户ID不能为空")
	}

	// 检查用户是否存在
	_, err := s.userDao.GetByID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return utils.NewAppError(utils.ErrUserNotFoundCode, "用户不存在")
		}
		return err
	}

	// 获取当前用户的角色
	currentRoles, err := s.userRoleDao.GetUserRoles(userID)
	if err != nil {
		return err
	}

	// 检查所有角色是否存在
	for _, roleID := range roleIDs {
		_, err := s.roleDao.GetByID(roleID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return utils.NewAppError(utils.ErrNotFoundCode, "角色不存在")
			}
			return err
		}
	}

	// 构建当前角色ID集合
	currentRoleIDs := make(map[uint]bool)
	for _, role := range currentRoles {
		currentRoleIDs[role.ID] = true
	}

	// 构建新角色ID集合
	newRoleIDs := make(map[uint]bool)
	for _, roleID := range roleIDs {
		newRoleIDs[roleID] = true
	}

	// 找出需要新增的角色
	var toAdd []uint
	for _, roleID := range roleIDs {
		if !currentRoleIDs[roleID] {
			toAdd = append(toAdd, roleID)
		}
	}

	// 找出需要删除的角色
	var toDelete []uint
	for _, role := range currentRoles {
		if !newRoleIDs[role.ID] {
			toDelete = append(toDelete, role.ID)
		}
	}

	// 批量新增
	if len(toAdd) > 0 {
		err = s.userRoleDao.CreateUserRoles(userID, toAdd)
		if err != nil {
			return err
		}
	}

	// 批量删除
	if len(toDelete) > 0 {
		err = s.userRoleDao.DeleteUserRoles(userID, toDelete)
		if err != nil {
			return err
		}
	}

	return nil
}

// GetUserRoles 获取用户的所有角色
func (s *userRoleService) GetUserRoles(userID uint) ([]*model.Role, error) {
	if userID == 0 {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "用户ID不能为空")
	}

	// 获取用户角色
	roles, err := s.userRoleDao.GetUserRoles(userID)
	if err != nil {
		return nil, err
	}

	return roles, nil
}

// GetRoleUsers 获取角色下的所有用户
func (s *userRoleService) GetRoleUsers(roleID uint) ([]*model.User, error) {
	if roleID == 0 {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "角色ID不能为空")
	}

	// 检查角色是否存在
	_, err := s.roleDao.GetByID(roleID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, utils.NewAppError(utils.ErrNotFoundCode, "角色不存在")
		}
		return nil, err
	}

	// 获取角色用户
	return s.userRoleDao.GetRoleUsers(roleID)
}

// GetUserRolesByUserIDs 批量获取多个用户的角色信息
func (s *userRoleService) GetUserRolesByUserIDs(userIDs []uint) ([]*model.UserRole, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}
	userRoles, err := s.userRoleDao.GetUserRolesByUserIDs(userIDs)
	if err != nil {
		return nil, err
	}

	return userRoles, nil
}
