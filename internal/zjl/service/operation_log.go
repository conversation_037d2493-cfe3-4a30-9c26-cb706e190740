package service

import (
	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/dao"
	"gitee.com/rcztcs/zjl/internal/zjl/model"
)

// OperationLogService 操作日志服务层接口
type OperationLogService interface {
	// RecordOperation 记录操作日志
	RecordOperation(userID uint, username string, operation model.OperationType, ip, userAgent string) error

	// ListOperationLogs 分页查询操作日志列表
	ListOperationLogs(page, pageSize int, userID *uint, operation string) ([]*model.OperationLog, int64, error)

	// ListUserOperationLogs 分页查询某个用户的操作日志
	ListUserOperationLogs(userID uint, page, pageSize int) ([]*model.OperationLog, int64, error)
}

// operationLogService 操作日志服务层实现
type operationLogService struct {
	operationLogDao dao.OperationLogDao
}

// NewOperationLogService 创建操作日志服务层实例
func NewOperationLogService(operationLogDao dao.OperationLogDao) OperationLogService {
	return &operationLogService{
		operationLogDao: operationLogDao,
	}
}

// RecordOperation 记录操作日志
func (s *operationLogService) RecordOperation(userID uint, username string, operation model.OperationType, ip, userAgent string) error {
	if userID == 0 {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "用户ID不能为空")
	}
	if username == "" {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "用户名不能为空")
	}
	if operation == "" {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "操作类型不能为空")
	}
	if ip == "" {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "IP地址不能为空")
	}

	// 创建操作日志
	log := &model.OperationLog{
		UserID:    userID,
		Username:  username,
		Operation: operation,
		IP:        ip,
		UserAgent: userAgent,
	}

	return s.operationLogDao.Create(log)
}

// ListOperationLogs 分页查询操作日志列表
func (s *operationLogService) ListOperationLogs(page, pageSize int, userID *uint, operation string) ([]*model.OperationLog, int64, error) {
	// 参数校验
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	// 查询操作日志列表
	logs, err := s.operationLogDao.List(page, pageSize, userID, operation)
	if err != nil {
		return nil, 0, err
	}

	// 查询总数
	total, err := s.operationLogDao.Count(userID, operation)
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// ListUserOperationLogs 分页查询某个用户的操作日志
func (s *operationLogService) ListUserOperationLogs(userID uint, page, pageSize int) ([]*model.OperationLog, int64, error) {
	if userID == 0 {
		return nil, 0, utils.NewAppError(utils.ErrInvalidParamsCode, "用户ID不能为空")
	}

	// 参数校验
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	// 查询用户操作日志列表
	logs, err := s.operationLogDao.ListByUserID(userID, page, pageSize)
	if err != nil {
		return nil, 0, err
	}

	// 查询总数
	total, err := s.operationLogDao.CountByUserID(userID)
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}
