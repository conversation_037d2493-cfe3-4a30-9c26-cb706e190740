package service

import (
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

type JWTService interface {
	GenerateToken(userID uint) (string, int64, error)
	GenerateTokenPair(userID uint) (accessToken string, accessExpire int64, refreshToken string, refreshExpire int64, error error)
	RefreshToken(tokenString string) (string, int64, error)
	RefreshAccessToken(refreshToken string) (accessToken string, accessExpire int64, error error)
	ValidateToken(tokenString string) bool
	ValidateRefreshToken(tokenString string) bool
	GetUserIDFromToken(tokenString string) (uint, error)
	GetUserIDFromRefreshToken(tokenString string) (uint, error)
}

// JWTClaims JWT声明
type JWTClaims struct {
	UserID uint `json:"user_id"`
	jwt.RegisteredClaims
}

// jwtService JWT服务实现
type jwtService struct {
	secretKey          string
	expireHours        int
	refreshExpireHours int
}

// NewJWTService 创建JWT服务实例
func NewJWTService(jwtKey string) JWTService {
	return &jwtService{
		secretKey:          jwtKey,
		expireHours:        24,  // 默认24小时
		refreshExpireHours: 720, // 默认30天 (30 * 24 = 720小时)
	}
}

// GenerateToken 生成JWT token
func (s *jwtService) GenerateToken(userID uint) (string, int64, error) {
	return s.GenerateTokenWithConfig(userID, s.secretKey, s.expireHours)
}

// GenerateTokenPair 生成访问令牌和刷新令牌对
func (s *jwtService) GenerateTokenPair(userID uint) (accessToken string, accessExpire int64, refreshToken string, refreshExpire int64, err error) {
	// 生成访问令牌
	accessToken, accessExpire, err = s.GenerateTokenWithConfig(userID, s.secretKey, s.expireHours)
	if err != nil {
		return "", 0, "", 0, err
	}

	// 生成刷新令牌
	refreshToken, refreshExpire, err = s.GenerateTokenWithConfig(userID, s.secretKey, s.refreshExpireHours)
	if err != nil {
		return "", 0, "", 0, err
	}

	return accessToken, accessExpire, refreshToken, refreshExpire, nil
}

// RefreshToken 刷新token
func (s *jwtService) RefreshToken(tokenString string) (string, int64, error) {
	return s.RefreshTokenWithSecret(tokenString, s.secretKey)
}

// RefreshAccessToken 使用刷新令牌生成新的访问令牌
func (s *jwtService) RefreshAccessToken(refreshToken string) (accessToken string, accessExpire int64, error error) {
	// 验证刷新令牌
	claims, err := s.ParseTokenWithSecret(refreshToken, s.secretKey)
	if err != nil {
		return "", 0, err
	}

	// 生成新的访问令牌
	return s.GenerateTokenWithConfig(claims.UserID, s.secretKey, s.expireHours)
}

// ValidateToken 验证token是否有效
func (s *jwtService) ValidateToken(tokenString string) bool {
	_, err := s.ParseTokenWithSecret(tokenString, s.secretKey)
	return err == nil
}

// ValidateRefreshToken 验证刷新令牌是否有效
func (s *jwtService) ValidateRefreshToken(tokenString string) bool {
	_, err := s.ParseTokenWithSecret(tokenString, s.secretKey)
	return err == nil
}

// GetUserIDFromToken 从token中获取用户ID
func (s *jwtService) GetUserIDFromToken(tokenString string) (uint, error) {
	claims, err := s.ParseTokenWithSecret(tokenString, s.secretKey)
	if err != nil {
		return 0, err
	}
	return claims.UserID, nil
}

// GetUserIDFromRefreshToken 从刷新令牌中获取用户ID
func (s *jwtService) GetUserIDFromRefreshToken(tokenString string) (uint, error) {
	claims, err := s.ParseTokenWithSecret(tokenString, s.secretKey)
	if err != nil {
		return 0, err
	}
	return claims.UserID, nil
}

// GenerateTokenWithConfig 使用自定义配置生成JWT token
func (s *jwtService) GenerateTokenWithConfig(userID uint, secretKey string, expireHours int) (string, int64, error) {
	now := time.Now()
	claims := &JWTClaims{
		UserID: userID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(time.Duration(expireHours) * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	expire := time.Now().Add(time.Duration(s.expireHours) * time.Hour)
	signedString, err := token.SignedString([]byte(secretKey))
	if err != nil {
		return "", 0, err
	}

	return signedString, expire.UnixMilli(), nil
}

// ParseTokenWithSecret 使用指定密钥解析JWT token
func (s *jwtService) ParseTokenWithSecret(tokenString, secretKey string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("无效的签名方法")
		}
		return []byte(secretKey), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("无效的token")
}

// RefreshTokenWithSecret 使用指定密钥刷新token
func (s *jwtService) RefreshTokenWithSecret(tokenString, secretKey string) (string, int64, error) {
	claims, err := s.ParseTokenWithSecret(tokenString, secretKey)
	if err != nil {
		return "", 0, err
	}

	// 检查token是否即将过期（剩余时间少于1小时）
	if time.Until(claims.ExpiresAt.Time) > time.Hour {
		return "", 0, errors.New("token尚未到刷新时间")
	}

	// 生成新token
	return s.GenerateTokenWithConfig(claims.UserID, secretKey, s.expireHours)
}
