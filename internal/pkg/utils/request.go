package utils

import (
	"errors"
	"github.com/gin-gonic/gin"
	"io"
	"net"
	"net/http"
	"strings"
)

type resp struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

type PaginationRequest interface {
	SetDefaultPagination()
}

// ParseRequest 统一解析请求参数
func ParseRequest(ctx *gin.Context, req interface{}) error {
	// 绑定URI参数
	if err := ctx.ShouldBindUri(req); err != nil {
		// URI参数绑定失败时不返回错误，因为可能没有URI参数
	}

	// 绑定Query参数
	if err := ctx.ShouldBindQuery(req); err != nil {
		// Query参数绑定失败时不返回错误，因为可能没有Query参数
	}

	// 绑定JSON参数
	if err := ctx.ShouldBindJSON(req); err != nil {
		if errors.Is(err, io.EOF) {

		} else {
			return NewAppError(ErrInvalidParamsCode, "请正确填写参数")
		}
	}

	// 处理分页参数
	if paginationReq, ok := req.(PaginationRequest); ok {
		paginationReq.SetDefaultPagination()
	}

	return nil
}

// ResponseSuccess 统一成功响应
func ResponseSuccess(ctx *gin.Context, data interface{}) {
	ctx.JSON(http.StatusOK, resp{
		Code:    0,
		Message: "ok",
		Data:    data,
	})
}

// ResponseList 统一成功响应
func ResponseList(ctx *gin.Context, data interface{}, count int64) {
	ctx.JSON(http.StatusOK, resp{
		Code:    0,
		Message: "ok",
		Data: map[string]any{
			"list":  data,
			"count": count,
		},
	})
}

// GetClientIP 获取客户端真实IP地址
func GetClientIP(ctx *gin.Context) string {
	// 优先从 X-Forwarded-For 头获取
	xForwardedFor := ctx.GetHeader("X-Forwarded-For")
	if xForwardedFor != "" {
		// X-Forwarded-For 可能包含多个IP，取第一个
		ips := strings.Split(xForwardedFor, ",")
		if len(ips) > 0 {
			ip := strings.TrimSpace(ips[0])
			if ip != "" && ip != "unknown" {
				return ip
			}
		}
	}

	// 从 X-Real-IP 头获取
	xRealIP := ctx.GetHeader("X-Real-IP")
	if xRealIP != "" && xRealIP != "unknown" {
		return xRealIP
	}

	// 从 X-Forwarded-For 头获取（备用）
	xForwarded := ctx.GetHeader("X-Forwarded")
	if xForwarded != "" && xForwarded != "unknown" {
		return xForwarded
	}

	// 从 Forwarded-For 头获取
	forwardedFor := ctx.GetHeader("Forwarded-For")
	if forwardedFor != "" && forwardedFor != "unknown" {
		return forwardedFor
	}

	// 从 Forwarded 头获取
	forwarded := ctx.GetHeader("Forwarded")
	if forwarded != "" && forwarded != "unknown" {
		return forwarded
	}

	// 最后从 RemoteAddr 获取
	ip, _, err := net.SplitHostPort(ctx.Request.RemoteAddr)
	if err != nil {
		return ctx.Request.RemoteAddr
	}
	return ip
}

// GetCurrentUserID 从上下文中获取当前用户ID
func GetCurrentUserID(ctx *gin.Context) (uint, bool) {
	userID, exists := ctx.Get("userID")
	if !exists {
		return 0, false
	}

	if id, ok := userID.(uint); ok {
		return id, true
	}

	return 0, false
}

// GetCurrentToken 从上下文中获取当前token
func GetCurrentToken(ctx *gin.Context) (string, bool) {
	token, exists := ctx.Get("token")
	if !exists {
		return "", false
	}

	if tokenStr, ok := token.(string); ok {
		return tokenStr, true
	}

	return "", false
}
