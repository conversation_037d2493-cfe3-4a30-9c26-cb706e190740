package config

import (
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

var conf *Config

type Config struct {
	JwtSecretKey string `yaml:"jwt_secret_key" validate:"required"`
	DbURL        string `yaml:"db_url" validate:"required"`
	UploadPath   string `yaml:"upload_path" validate:"required"`
}

// Init 初始化配置，从YAML文件中读取配置
func Init() error {
	return InitWithFile("config/dev.yaml")
}

// InitWithFile 从指定的YAML文件中读取配置
func InitWithFile(configPath string) error {
	// 获取当前工作目录
	workDir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("获取工作目录失败: %w", err)
	}

	// 构建配置文件的完整路径
	fullPath := filepath.Join(workDir, configPath)

	// 检查文件是否存在
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		return fmt.Errorf("配置文件不存在: %s", fullPath)
	}

	// 读取YAML文件
	data, err := os.ReadFile(fullPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析YAML到配置结构体
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("解析YAML配置失败: %w", err)
	}

	// 验证必填字段
	if config.DbURL == "" {
		return fmt.Errorf("数据库URL不能为空")
	}

	// 设置全局配置
	conf = &config
	return nil
}

func GetConfig() *Config {
	return conf
}
