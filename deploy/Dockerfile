# 构建阶段
FROM golang:1.24.5-alpine3.22 AS builder

WORKDIR /app
RUN apk add --no-cache git

COPY . .
RUN go mod tidy

RUN CGO_ENABLED=0 GOOS=linux go build -o zjl ./cmd/zjl

# 运行阶段
FROM alpine:3.22

RUN apk --no-cache add ca-certificates tzdata
ENV TZ=Asia/Shanghai

RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

WORKDIR /app

COPY --from=builder /app/zjl .
COPY --from=builder /app/config ./config
COPY --from=builder /app/frontend ./frontend

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories

RUN apk update && apk add tzdata
RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo "Asia/Shanghai" > /etc/timezone

RUN mkdir -p uploads && \
    chown -R appuser:appgroup /app

USER appuser
EXPOSE 8080
CMD ["./zjl", "-c", "./config/prd.yaml"]
