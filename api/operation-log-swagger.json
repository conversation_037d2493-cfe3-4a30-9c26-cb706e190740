{"openapi": "3.0.0", "info": {"title": "操作日志管理API", "description": "操作日志管理相关接口", "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080/api", "description": "开发环境"}], "paths": {"/operation-logs": {"get": {"summary": "分页查询操作日志列表", "description": "分页查询最近的操作日志记录，支持按用户ID和操作类型筛选", "tags": ["操作日志"], "parameters": [{"name": "page", "in": "query", "description": "页码，默认1", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "page_size", "in": "query", "description": "每页数量，默认10", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "user_id", "in": "query", "description": "用户ID筛选", "required": false, "schema": {"type": "integer", "format": "int64", "minimum": 1}}, {"name": "operation", "in": "query", "description": "操作类型筛选", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "ok"}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/OperationLog"}}, "count": {"type": "integer", "format": "int64", "description": "总记录数"}}}}}}}}}}}, "/operation-logs/users/{user_id}": {"get": {"summary": "分页查询指定用户的操作日志", "description": "分页查询某个用户的操作日志记录", "tags": ["操作日志"], "parameters": [{"name": "user_id", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "integer", "format": "int64", "minimum": 1}}, {"name": "page", "in": "query", "description": "页码，默认1", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "page_size", "in": "query", "description": "每页数量，默认10", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "ok"}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/OperationLog"}}, "count": {"type": "integer", "format": "int64", "description": "总记录数"}}}}}}}}}}}}, "components": {"schemas": {"OperationLog": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "操作日志ID"}, "user_id": {"type": "integer", "format": "int64", "description": "用户ID"}, "username": {"type": "string", "description": "用户名"}, "operation": {"type": "string", "description": "操作类型"}, "ip": {"type": "string", "description": "IP地址"}, "user_agent": {"type": "string", "description": "用户代理"}, "description": {"type": "string", "description": "操作描述"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}, "required": ["id", "user_id", "username", "operation", "ip"]}}}}