{"openapi": "3.0.3", "info": {"title": "文件管理API", "description": "文件管理相关的RESTful API接口文档，包括文件上传、下载、查询和删除功能", "version": "1.0.0", "contact": {"name": "API Support", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:8080/api", "description": "开发环境"}], "paths": {"/files/upload": {"post": {"tags": ["文件管理"], "summary": "文件上传", "description": "上传文件到服务器，支持多种文件类型，最大10MB", "operationId": "uploadFile", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "要上传的文件"}}, "required": ["file"]}}}}, "responses": {"200": {"description": "上传成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/UploadFileResponse"}}}]}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "2002": {"description": "文件过大", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "2003": {"description": "文件类型不允许", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "2004": {"description": "文件上传失败", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/files/download/{storage_name}": {"get": {"tags": ["文件管理"], "summary": "文件下载", "description": "根据存储文件名下载文件", "operationId": "downloadFile", "parameters": [{"name": "storage_name", "in": "path", "required": true, "description": "存储文件名", "schema": {"type": "string", "example": "1704110400000000000.pdf"}}], "responses": {"200": {"description": "下载成功", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}, "headers": {"Content-Disposition": {"description": "文件下载头", "schema": {"type": "string", "example": "attachment; filename=example.pdf"}}, "Content-Length": {"description": "文件大小", "schema": {"type": "integer"}}}}, "2001": {"$ref": "#/components/responses/FileNotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/files/{storage_name}": {"get": {"tags": ["文件管理"], "summary": "获取文件信息", "description": "根据存储文件名获取文件详细信息", "operationId": "getFileInfo", "parameters": [{"name": "storage_name", "in": "path", "required": true, "description": "存储文件名", "schema": {"type": "string", "example": "1704110400000000000.pdf"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/File"}}}]}}}}, "2001": {"$ref": "#/components/responses/FileNotFound"}}}, "delete": {"tags": ["文件管理"], "summary": "删除文件", "description": "根据存储文件名删除文件（软删除）", "operationId": "deleteFile", "parameters": [{"name": "storage_name", "in": "path", "required": true, "description": "存储文件名", "schema": {"type": "string", "example": "1704110400000000000.pdf"}}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "2001": {"$ref": "#/components/responses/FileNotFound"}}}}, "/files": {"get": {"tags": ["文件管理"], "summary": "获取文件列表", "description": "分页查询文件列表，支持按上传用户和文件类型筛选", "operationId": "listFiles", "parameters": [{"name": "page", "in": "query", "description": "页码，默认为1", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "page_size", "in": "query", "description": "每页数量，默认为10", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "upload_user_id", "in": "query", "description": "上传用户ID筛选，可选", "required": false, "schema": {"type": "integer", "format": "int64", "minimum": 1}}, {"name": "content_type", "in": "query", "description": "文件类型筛选，可选", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/ListFilesResponse"}}}]}}}}, "400": {"$ref": "#/components/responses/BadRequest"}}}}}, "components": {"schemas": {"File": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "文件ID", "example": 1}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间", "example": "2024-01-01T12:00:00Z"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间", "example": "2024-01-01T12:00:00Z"}, "deleted_at": {"type": "string", "format": "date-time", "nullable": true, "description": "删除时间", "example": null}, "file_name": {"type": "string", "description": "原始文件名", "example": "document.pdf"}, "storage_name": {"type": "string", "description": "存储文件名（系统生成的唯一文件名）", "example": "1704110400000000000.pdf"}, "file_path": {"type": "string", "description": "文件存储路径", "example": "./uploads/1704110400000000000.pdf"}, "file_size": {"type": "integer", "format": "int64", "description": "文件大小（字节）", "example": 1048576}, "content_type": {"type": "string", "description": "文件MIME类型", "example": "application/pdf"}, "file_hash": {"type": "string", "description": "文件MD5哈希值（用于去重）", "example": "d41d8cd98f00b204e9800998ecf8427e"}, "upload_user_id": {"type": "integer", "format": "int64", "description": "上传用户ID", "example": 1}, "status": {"type": "integer", "description": "文件状态（1-正常，2-已删除）", "enum": [1, 2], "example": 1}, "download_count": {"type": "integer", "format": "int64", "description": "下载次数", "example": 5}, "last_download_at": {"type": "string", "format": "date-time", "nullable": true, "description": "最后下载时间", "example": "2024-01-01T15:30:00Z"}}}, "UploadFileResponse": {"type": "object", "properties": {"storage_name": {"type": "string", "description": "存储文件名", "example": "1704110400000000000.pdf"}}, "required": ["storage_name"]}, "ListFilesResponse": {"type": "object", "properties": {"list": {"type": "array", "description": "文件列表", "items": {"$ref": "#/components/schemas/File"}}, "count": {"type": "integer", "format": "int64", "description": "总记录数", "example": 100}}}, "SuccessResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码，0表示成功", "example": 0}, "message": {"type": "string", "description": "响应消息", "example": "ok"}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "message": {"type": "string", "description": "错误消息"}}, "required": ["code", "message"]}}, "responses": {"BadRequest": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 400, "message": "参数错误"}}}}, "FileNotFound": {"description": "文件不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 2001, "message": "文件不存在"}}}}, "InternalServerError": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 500, "message": "服务器内部错误"}}}}}}, "tags": [{"name": "文件管理", "description": "文件上传、下载、查询和删除相关操作"}], "x-file-constraints": {"max_file_size": "10MB (10485760 bytes)", "supported_extensions": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt", ".zip", ".rar"], "upload_path": "./uploads", "hash_algorithm": "MD5"}, "x-error-codes": {"400": "参数错误", "401": "未授权", "403": "禁止访问", "404": "资源不存在", "500": "服务器内部错误", "2001": "文件不存在", "2002": "文件过大", "2003": "文件类型不允许", "2004": "文件上传失败"}}