{"openapi": "3.0.3", "info": {"title": "角色管理API", "description": "角色管理相关的RESTful API接口文档，遵循RESTful设计规范", "version": "1.0.0", "contact": {"name": "API Support", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:8080/api", "description": "开发环境"}], "paths": {"/roles": {"post": {"tags": ["角色管理"], "summary": "创建角色", "description": "创建新的角色", "operationId": "createRole", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRoleRequest"}}}}, "responses": {"200": {"description": "创建成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/CreateRoleResponse"}}}]}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "get": {"tags": ["角色管理"], "summary": "角色列表查询", "description": "分页查询角色列表，支持按角色名称筛选", "operationId": "listRoles", "parameters": [{"name": "page", "in": "query", "description": "页码，默认为1", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "page_size", "in": "query", "description": "每页数量，默认为10", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "name", "in": "query", "description": "角色名称筛选（模糊查询）", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}}, "total": {"type": "integer", "description": "总记录数"}}}]}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/roles/{id}": {"put": {"tags": ["角色管理"], "summary": "更新角色", "description": "更新指定角色的信息", "operationId": "updateRole", "parameters": [{"name": "id", "in": "path", "description": "角色ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleRequest"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["角色管理"], "summary": "删除角色", "description": "删除指定的角色", "operationId": "deleteRole", "parameters": [{"name": "id", "in": "path", "description": "角色ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"schemas": {"Role": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "角色ID"}, "name": {"type": "string", "description": "角色名称"}, "description": {"type": "string", "description": "角色描述"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}, "required": ["id", "name", "description"]}, "CreateRoleRequest": {"type": "object", "properties": {"name": {"type": "string", "description": "角色名称", "minLength": 1, "maxLength": 100}, "description": {"type": "string", "description": "角色描述", "minLength": 1, "maxLength": 255}}, "required": ["name", "description"]}, "CreateRoleResponse": {"type": "object", "properties": {"role": {"$ref": "#/components/schemas/Role"}}}, "UpdateRoleRequest": {"type": "object", "properties": {"name": {"type": "string", "description": "角色名称", "minLength": 1, "maxLength": 100}, "description": {"type": "string", "description": "角色描述", "minLength": 1, "maxLength": 255}}, "required": ["name", "description"]}, "SuccessResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码，0表示成功", "example": 0}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "message": {"type": "string", "description": "错误信息"}}}}, "responses": {"BadRequest": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "NotFound": {"description": "资源不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "InternalServerError": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "tags": [{"name": "角色管理", "description": "角色相关的管理操作"}]}