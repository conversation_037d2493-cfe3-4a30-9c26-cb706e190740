{"openapi": "3.0.3", "info": {"title": "用户角色关联管理API", "description": "用户角色关联管理相关的RESTful API接口文档，遵循RESTful设计规范", "version": "1.0.0", "contact": {"name": "API Support", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:8080/api", "description": "开发环境"}], "paths": {"/user-roles/{user_id}/roles": {"post": {"tags": ["用户角色关联管理"], "summary": "创建用户角色关联", "description": "为用户关联多个角色", "operationId": "createUserRoles", "parameters": [{"name": "user_id", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserRolesRequest"}}}}, "responses": {"200": {"description": "关联成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["用户角色关联管理"], "summary": "删除用户角色关联", "description": "移除用户的多个角色关联", "operationId": "deleteUserRoles", "parameters": [{"name": "user_id", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteUserRolesRequest"}}}}, "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "get": {"tags": ["用户角色关联管理"], "summary": "获取用户角色", "description": "查询用户的所有角色", "operationId": "getUserRoles", "parameters": [{"name": "user_id", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/UserRolesResponse"}}}]}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"schemas": {"CreateUserRolesRequest": {"type": "object", "properties": {"role_ids": {"type": "array", "items": {"type": "integer", "format": "int64", "minimum": 1}, "description": "角色ID列表", "minItems": 1}}, "required": ["role_ids"]}, "DeleteUserRolesRequest": {"type": "object", "properties": {"role_ids": {"type": "array", "items": {"type": "integer", "format": "int64", "minimum": 1}, "description": "角色ID列表", "minItems": 1}}, "required": ["role_ids"]}, "UserRolesResponse": {"type": "object", "properties": {"user_id": {"type": "integer", "format": "int64", "description": "用户ID"}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}, "description": "角色列表"}}, "required": ["user_id", "roles"]}, "Role": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "角色ID"}, "name": {"type": "string", "description": "角色名称"}, "description": {"type": "string", "description": "角色描述"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}, "required": ["id", "name", "description"]}, "SuccessResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码，0表示成功", "example": 0}, "message": {"type": "string", "description": "响应消息", "example": "ok"}}, "required": ["code", "message"]}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "message": {"type": "string", "description": "错误消息"}}, "required": ["code", "message"]}}, "responses": {"BadRequest": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "NotFound": {"description": "资源不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "InternalServerError": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}