{"openapi": "3.0.3", "info": {"title": "权限管理API", "description": "权限管理相关的RESTful API接口文档，遵循RESTful设计规范", "version": "1.0.0", "contact": {"name": "API Support", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:8080/api", "description": "开发环境"}], "paths": {"/permissions": {"post": {"tags": ["权限管理"], "summary": "创建权限", "description": "创建新的权限", "operationId": "createPermission", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePermissionRequest"}}}}, "responses": {"200": {"description": "创建成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/CreatePermissionResponse"}}}]}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "get": {"tags": ["权限管理"], "summary": "权限列表查询", "description": "分页查询权限列表，支持按模块、资源、操作筛选", "operationId": "listPermissions", "parameters": [{"name": "page", "in": "query", "description": "页码，默认为1", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "page_size", "in": "query", "description": "每页数量，默认为10", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "module", "in": "query", "description": "模块筛选（模糊查询）", "required": false, "schema": {"type": "string"}}, {"name": "resource", "in": "query", "description": "资源筛选（模糊查询）", "required": false, "schema": {"type": "string"}}, {"name": "action", "in": "query", "description": "操作筛选（模糊查询）", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Permission"}}, "total": {"type": "integer", "description": "总记录数"}}}]}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/permissions/{id}": {"put": {"tags": ["权限管理"], "summary": "更新权限", "description": "更新指定权限的信息", "operationId": "updatePermission", "parameters": [{"name": "id", "in": "path", "description": "权限ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePermissionRequest"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"schemas": {"Permission": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "权限ID"}, "name": {"type": "string", "description": "权限名称"}, "module": {"type": "string", "description": "所属模块"}, "resource": {"type": "string", "description": "资源类型"}, "action": {"type": "string", "description": "操作类型"}, "description": {"type": "string", "description": "权限描述"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}, "required": ["id", "name", "module", "resource", "action", "description"]}, "CreatePermissionRequest": {"type": "object", "properties": {"name": {"type": "string", "description": "权限名称", "minLength": 1, "maxLength": 100}, "module": {"type": "string", "description": "所属模块", "minLength": 1, "maxLength": 50}, "resource": {"type": "string", "description": "资源类型", "minLength": 1, "maxLength": 50}, "action": {"type": "string", "description": "操作类型", "minLength": 1, "maxLength": 50}, "description": {"type": "string", "description": "权限描述", "minLength": 1, "maxLength": 255}}, "required": ["name", "module", "resource", "action", "description"]}, "CreatePermissionResponse": {"type": "object", "properties": {"permission": {"$ref": "#/components/schemas/Permission"}}}, "UpdatePermissionRequest": {"type": "object", "properties": {"name": {"type": "string", "description": "权限名称", "minLength": 1, "maxLength": 100}, "module": {"type": "string", "description": "所属模块", "minLength": 1, "maxLength": 50}, "resource": {"type": "string", "description": "资源类型", "minLength": 1, "maxLength": 50}, "action": {"type": "string", "description": "操作类型", "minLength": 1, "maxLength": 50}, "description": {"type": "string", "description": "权限描述", "minLength": 1, "maxLength": 255}}, "required": ["name", "module", "resource", "action", "description"]}, "SuccessResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码，0表示成功", "example": 0}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "message": {"type": "string", "description": "错误信息"}}}}, "responses": {"BadRequest": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "NotFound": {"description": "资源不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "InternalServerError": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "tags": [{"name": "权限管理", "description": "权限相关的管理操作"}]}