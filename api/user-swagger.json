{
  "openapi": "3.0.3",
  "info": {
    "title": "用户管理API",
    "description": "用户管理相关的RESTful API接口文档，遵循RESTful设计规范",
    "version": "1.0.0",
    "contact": {
      "name": "API Support",
      "email": "<EMAIL>"
    }
  },
  "servers": [
    {
      "url": "http://localhost:8080/api",
      "description": "开发环境"
    }
  ],
  "paths": {
    "/users/login": {
      "post": {
        "tags": [
          "用户管理"
        ],
        "summary": "用户登录",
        "description": "用户通过用户名和密码进行登录认证",
        "operationId": "userLogin",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/LoginRequest"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "登录成功",
            "content": {
              "application/json": {
                "schema": {
                  "allOf": [
                    {
                      "$ref": "#/components/schemas/SuccessResponse"
                    },
                    {
                      "type": "object",
                      "properties": {
                        "data": {
                          "$ref": "#/components/schemas/LoginResponse"
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          "400": {
            "description": "参数错误",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ErrorResponse"
                }
              }
            }
          },
          "1003": {
            "description": "用户名或密码错误",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ErrorResponse"
                }
              }
            }
          },
          "1002": {
            "description": "用户已被禁用",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ErrorResponse"
                }
              }
            }
          }
        }
      }
    },
    "/users/refresh": {
      "post": {
        "tags": [
          "用户管理"
        ],
        "summary": "刷新访问令牌",
        "description": "使用刷新令牌获取新的访问令牌",
        "operationId": "refreshToken",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/RefreshTokenRequest"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "刷新成功",
            "content": {
              "application/json": {
                "schema": {
                  "allOf": [
                    {
                      "$ref": "#/components/schemas/SuccessResponse"
                    },
                    {
                      "type": "object",
                      "properties": {
                        "data": {
                          "$ref": "#/components/schemas/RefreshTokenResponse"
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          "400": {
            "description": "参数错误",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ErrorResponse"
                }
              }
            }
          },
          "401": {
            "description": "刷新令牌无效或已过期",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ErrorResponse"
                }
              }
            }
          }
        }
      }
    },
    "/users": {
      "get": {
        "tags": [
          "用户管理"
        ],
        "summary": "获取用户列表",
        "description": "分页获取用户列表，支持状态筛选和名称搜索",
        "operationId": "listUsers",
        "parameters": [
          {
            "name": "page",
            "in": "query",
            "required": false,
            "description": "页码，默认1",
            "schema": {
              "type": "integer",
              "minimum": 1,
              "default": 1
            }
          },
          {
            "name": "page_size",
            "in": "query",
            "required": false,
            "description": "每页数量，默认10",
            "schema": {
              "type": "integer",
              "minimum": 1,
              "maximum": 100,
              "default": 10
            }
          },
          {
            "name": "status",
            "in": "query",
            "required": false,
            "description": "用户状态筛选：0-禁用，1-启用",
            "schema": {
              "type": "integer",
              "enum": [0, 1]
            }
          },
          {
            "name": "name",
            "in": "query",
            "required": false,
            "description": "用户名或昵称模糊搜索",
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "获取成功",
            "content": {
              "application/json": {
                "schema": {
                  "allOf": [
                    {
                      "$ref": "#/components/schemas/SuccessResponse"
                    },
                    {
                      "type": "object",
                      "properties": {
                        "data": {
                          "$ref": "#/components/schemas/ListUsersResponse"
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          "400": {
            "description": "参数错误",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ErrorResponse"
                }
              }
            }
          }
        }
      },
      "post": {
        "tags": [
          "用户管理"
        ],
        "summary": "创建用户",
        "description": "创建新的用户账号",
        "operationId": "createUser",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateUserRequest"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "创建成功",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SuccessResponse"
                }
              }
            }
          },
          "400": {
            "description": "参数错误",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ErrorResponse"
                }
              }
            }
          },
          "1004": {
            "description": "用户名已存在",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ErrorResponse"
                }
              }
            }
          }
        }
      }
    },
    "/users/{id}": {
      "get": {
        "tags": [
          "用户管理"
        ],
        "summary": "获取用户信息",
        "description": "根据用户ID获取用户详细信息",
        "operationId": "getUserInfo",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "description": "用户ID",
            "schema": {
              "type": "integer",
              "format": "int64",
              "minimum": 1
            }
          }
        ],
        "responses": {
          "200": {
            "description": "获取成功",
            "content": {
              "application/json": {
                "schema": {
                  "allOf": [
                    {
                      "$ref": "#/components/schemas/SuccessResponse"
                    },
                    {
                      "type": "object",
                      "properties": {
                        "data": {
                          "$ref": "#/components/schemas/GetUserResponse"
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          "400": {
            "description": "参数错误",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ErrorResponse"
                }
              }
            }
          },
          "1001": {
            "description": "用户不存在",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ErrorResponse"
                }
              }
            }
          }
        }
      }
    },
    "/users/{id}/password": {
      "put": {
        "tags": [
          "用户管理"
        ],
        "summary": "重置用户密码",
        "description": "重置指定用户的密码",
        "operationId": "resetPassword",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "description": "用户ID",
            "schema": {
              "type": "integer",
              "format": "int64",
              "minimum": 1
            }
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ResetPasswordRequest"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "重置成功",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SuccessResponse"
                }
              }
            }
          },
          "400": {
            "description": "参数错误",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ErrorResponse"
                }
              }
            }
          },
          "1001": {
            "description": "用户不存在",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ErrorResponse"
                }
              }
            }
          }
        }
      }
    },

  },
  "components": {
    "schemas": {
      "LoginRequest": {
        "type": "object",
        "required": [
          "username",
          "password"
        ],
        "properties": {
          "username": {
            "type": "string",
            "description": "用户名",
            "minLength": 1
          },
          "password": {
            "type": "string",
            "description": "密码",
            "minLength": 1
          }
        }
      },
      "CreateUserRequest": {
        "type": "object",
        "required": [
          "name",
          "username",
          "password"
        ],
        "properties": {
          "name": {
            "type": "string",
            "description": "姓名",
            "minLength": 1
          },
          "username": {
            "type": "string",
            "description": "用户名",
            "minLength": 1
          },
          "password": {
            "type": "string",
            "description": "密码",
            "minLength": 1
          },
          "mobile": {
            "type": "string",
            "description": "手机号",
            "pattern": "^1[3-9]\\d{9}$"
          }
        }
      },
      "User": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int64",
            "description": "用户ID"
          },
          "name": {
            "type": "string",
            "description": "姓名"
          },
          "username": {
            "type": "string",
            "description": "用户名"
          },
          "mobile": {
            "type": "string",
            "description": "手机号"
          },
          "status": {
            "type": "integer",
            "description": "状态：0-禁用，1-启用",
            "enum": [0, 1]
          },
          "created_at": {
            "type": "integer",
            "format": "int64",
            "description": "创建时间（毫秒时间戳）"
          },
          "updated_at": {
            "type": "integer",
            "format": "int64",
            "description": "更新时间（毫秒时间戳）"
          }
        }
      },
      "LoginResponse": {
        "type": "object",
        "properties": {
          "user": {
            "$ref": "#/components/schemas/User"
          }
        }
      },
      "GetUserResponse": {
        "type": "object",
        "properties": {
          "user": {
            "$ref": "#/components/schemas/User"
          }
        }
      },
      "ResetPasswordRequest": {
        "type": "object",
        "required": [
          "password"
        ],
        "properties": {
          "password": {
            "type": "string",
            "description": "新密码",
            "minLength": 1
          }
        }
      },
      "ListUsersResponse": {
        "type": "object",
        "properties": {
          "users": {
            "type": "array",
            "description": "用户列表",
            "items": {
              "$ref": "#/components/schemas/User"
            }
          },
          "total": {
            "type": "integer",
            "format": "int64",
            "description": "总数"
          }
        }
      },
      "RefreshTokenRequest": {
        "type": "object",
        "required": [
          "refresh_token"
        ],
        "properties": {
          "refresh_token": {
            "type": "string",
            "description": "刷新令牌",
            "minLength": 1
          }
        }
      },
      "RefreshTokenResponse": {
        "type": "object",
        "properties": {
          "access_token": {
            "type": "string",
            "description": "新的访问令牌"
          },
          "expires": {
            "type": "integer",
            "format": "int64",
            "description": "访问令牌过期时间（毫秒时间戳）"
          }
        }
      },
      "SuccessResponse": {
        "type": "object",
        "properties": {
          "code": {
            "type": "integer",
            "description": "响应码，0表示成功",
            "example": 0
          }
        }
      },
      "ErrorResponse": {
        "type": "object",
        "properties": {
          "code": {
            "type": "integer",
            "description": "错误码"
          },
          "message": {
            "type": "string",
            "description": "错误信息"
          }
        }
      }
    }
  },
  "tags": [
    {
      "name": "用户管理",
      "description": "用户相关的管理操作"
    }
  ]
}
